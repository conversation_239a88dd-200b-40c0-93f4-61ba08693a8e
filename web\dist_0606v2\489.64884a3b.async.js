"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[489],{47046:function(Y,w){var t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};w.Z=t},63783:function(Y,w,t){var c=t(1413),M=t(67294),T=t(36688),z=t(91146),Z=function(W,B){return M.createElement(z.Z,(0,c.Z)((0,c.Z)({},W),{},{ref:B,icon:T.Z}))},b=M.forwardRef(Z);w.Z=b},88484:function(Y,w,t){t.d(w,{Z:function(){return W}});var c=t(1413),M=t(67294),T={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},z=T,Z=t(91146),b=function(L,$){return M.createElement(Z.Z,(0,c.Z)((0,c.Z)({},L),{},{ref:$,icon:z}))},A=M.forwardRef(b),W=A},96074:function(Y,w,t){t.d(w,{Z:function(){return x}});var c=t(67294),M=t(93967),T=t.n(M),z=t(53124),Z=t(98675),b=t(11568),A=t(14747),W=t(83559),B=t(83262);const L=r=>{const{componentCls:l}=r;return{[l]:{"&-horizontal":{[`&${l}`]:{"&-sm":{marginBlock:r.marginXS},"&-md":{marginBlock:r.margin}}}}}},$=r=>{const{componentCls:l,sizePaddingEdgeHorizontal:h,colorSplit:u,lineWidth:g,textPaddingInline:O,orientationMargin:y,verticalMarginInline:P}=r;return{[l]:Object.assign(Object.assign({},(0,A.Wf)(r)),{borderBlockStart:`${(0,b.bf)(g)} solid ${u}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:P,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,b.bf)(g)} solid ${u}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,b.bf)(r.marginLG)} 0`},[`&-horizontal${l}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,b.bf)(r.dividerHorizontalWithTextGutterMargin)} 0`,color:r.colorTextHeading,fontWeight:500,fontSize:r.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${u}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,b.bf)(g)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${l}-with-text-start`]:{"&::before":{width:`calc(${y} * 100%)`},"&::after":{width:`calc(100% - ${y} * 100%)`}},[`&-horizontal${l}-with-text-end`]:{"&::before":{width:`calc(100% - ${y} * 100%)`},"&::after":{width:`calc(${y} * 100%)`}},[`${l}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:O},"&-dashed":{background:"none",borderColor:u,borderStyle:"dashed",borderWidth:`${(0,b.bf)(g)} 0 0`},[`&-horizontal${l}-with-text${l}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${l}-dashed`]:{borderInlineStartWidth:g,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:u,borderStyle:"dotted",borderWidth:`${(0,b.bf)(g)} 0 0`},[`&-horizontal${l}-with-text${l}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${l}-dotted`]:{borderInlineStartWidth:g,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${l}-with-text`]:{color:r.colorText,fontWeight:"normal",fontSize:r.fontSize},[`&-horizontal${l}-with-text-start${l}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${l}-inner-text`]:{paddingInlineStart:h}},[`&-horizontal${l}-with-text-end${l}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${l}-inner-text`]:{paddingInlineEnd:h}}})}},U=r=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:r.marginXS});var H=(0,W.I$)("Divider",r=>{const l=(0,B.IX)(r,{dividerHorizontalWithTextGutterMargin:r.margin,sizePaddingEdgeHorizontal:0});return[$(l),L(l)]},U,{unitless:{orientationMargin:!0}}),C=function(r,l){var h={};for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&l.indexOf(u)<0&&(h[u]=r[u]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var g=0,u=Object.getOwnPropertySymbols(r);g<u.length;g++)l.indexOf(u[g])<0&&Object.prototype.propertyIsEnumerable.call(r,u[g])&&(h[u[g]]=r[u[g]]);return h};const I={small:"sm",middle:"md"};var x=r=>{const{getPrefixCls:l,direction:h,className:u,style:g}=(0,z.dj)("divider"),{prefixCls:O,type:y="horizontal",orientation:P="center",orientationMargin:j,className:G,rootClassName:p,children:k,dashed:Q,variant:J="solid",plain:D,style:V,size:e}=r,n=C(r,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),i=l("divider",O),[a,d,v]=H(i),S=(0,Z.Z)(e),o=I[S],s=!!k,f=c.useMemo(()=>P==="left"?h==="rtl"?"end":"start":P==="right"?h==="rtl"?"start":"end":P,[h,P]),E=f==="start"&&j!=null,N=f==="end"&&j!=null,q=T()(i,u,d,v,`${i}-${y}`,{[`${i}-with-text`]:s,[`${i}-with-text-${f}`]:s,[`${i}-dashed`]:!!Q,[`${i}-${J}`]:J!=="solid",[`${i}-plain`]:!!D,[`${i}-rtl`]:h==="rtl",[`${i}-no-default-orientation-margin-start`]:E,[`${i}-no-default-orientation-margin-end`]:N,[`${i}-${o}`]:!!o},G,p),K=c.useMemo(()=>typeof j=="number"?j:/^\d+$/.test(j)?Number(j):j,[j]),_={marginInlineStart:E?K:void 0,marginInlineEnd:N?K:void 0};return a(c.createElement("div",Object.assign({className:q,style:Object.assign(Object.assign({},g),V)},n,{role:"separator"}),k&&y!=="vertical"&&c.createElement("span",{className:`${i}-inner-text`,style:_},k)))}},99134:function(Y,w,t){var c=t(67294);const M=(0,c.createContext)({});w.Z=M},21584:function(Y,w,t){var c=t(67294),M=t(93967),T=t.n(M),z=t(53124),Z=t(99134),b=t(6999),A=function($,U){var H={};for(var C in $)Object.prototype.hasOwnProperty.call($,C)&&U.indexOf(C)<0&&(H[C]=$[C]);if($!=null&&typeof Object.getOwnPropertySymbols=="function")for(var I=0,C=Object.getOwnPropertySymbols($);I<C.length;I++)U.indexOf(C[I])<0&&Object.prototype.propertyIsEnumerable.call($,C[I])&&(H[C[I]]=$[C[I]]);return H};function W($){return typeof $=="number"?`${$} ${$} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test($)?`0 0 ${$}`:$}const B=["xs","sm","md","lg","xl","xxl"],L=c.forwardRef(($,U)=>{const{getPrefixCls:H,direction:C}=c.useContext(z.E_),{gutter:I,wrap:m}=c.useContext(Z.Z),{prefixCls:x,span:r,order:l,offset:h,push:u,pull:g,className:O,children:y,flex:P,style:j}=$,G=A($,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),p=H("col",x),[k,Q,J]=(0,b.cG)(p),D={};let V={};B.forEach(i=>{let a={};const d=$[i];typeof d=="number"?a.span=d:typeof d=="object"&&(a=d||{}),delete G[i],V=Object.assign(Object.assign({},V),{[`${p}-${i}-${a.span}`]:a.span!==void 0,[`${p}-${i}-order-${a.order}`]:a.order||a.order===0,[`${p}-${i}-offset-${a.offset}`]:a.offset||a.offset===0,[`${p}-${i}-push-${a.push}`]:a.push||a.push===0,[`${p}-${i}-pull-${a.pull}`]:a.pull||a.pull===0,[`${p}-rtl`]:C==="rtl"}),a.flex&&(V[`${p}-${i}-flex`]=!0,D[`--${p}-${i}-flex`]=W(a.flex))});const e=T()(p,{[`${p}-${r}`]:r!==void 0,[`${p}-order-${l}`]:l,[`${p}-offset-${h}`]:h,[`${p}-push-${u}`]:u,[`${p}-pull-${g}`]:g},O,V,Q,J),n={};if(I&&I[0]>0){const i=I[0]/2;n.paddingLeft=i,n.paddingRight=i}return P&&(n.flex=W(P),m===!1&&!n.minWidth&&(n.minWidth=0)),k(c.createElement("div",Object.assign({},G,{style:Object.assign(Object.assign(Object.assign({},n),j),D),className:e,ref:U}),y))});w.Z=L},17621:function(Y,w,t){t.d(w,{Z:function(){return I}});var c=t(67294),M=t(93967),T=t.n(M),z=t(74443),Z=t(53124),b=t(25378);function A(m,x){const r=[void 0,void 0],l=Array.isArray(m)?m:[m,void 0],h=x||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return l.forEach((u,g)=>{if(typeof u=="object"&&u!==null)for(let O=0;O<z.c4.length;O++){const y=z.c4[O];if(h[y]&&u[y]!==void 0){r[g]=u[y];break}}else r[g]=u}),r}var W=t(99134),B=t(6999),L=function(m,x){var r={};for(var l in m)Object.prototype.hasOwnProperty.call(m,l)&&x.indexOf(l)<0&&(r[l]=m[l]);if(m!=null&&typeof Object.getOwnPropertySymbols=="function")for(var h=0,l=Object.getOwnPropertySymbols(m);h<l.length;h++)x.indexOf(l[h])<0&&Object.prototype.propertyIsEnumerable.call(m,l[h])&&(r[l[h]]=m[l[h]]);return r};const $=null,U=null;function H(m,x){const[r,l]=c.useState(typeof m=="string"?m:""),h=()=>{if(typeof m=="string"&&l(m),typeof m=="object")for(let u=0;u<z.c4.length;u++){const g=z.c4[u];if(!x||!x[g])continue;const O=m[g];if(O!==void 0){l(O);return}}};return c.useEffect(()=>{h()},[JSON.stringify(m),x]),r}var I=c.forwardRef((m,x)=>{const{prefixCls:r,justify:l,align:h,className:u,style:g,children:O,gutter:y=0,wrap:P}=m,j=L(m,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:G,direction:p}=c.useContext(Z.E_),k=(0,b.Z)(!0,null),Q=H(h,k),J=H(l,k),D=G("row",r),[V,e,n]=(0,B.VM)(D),i=A(y,k),a=T()(D,{[`${D}-no-wrap`]:P===!1,[`${D}-${J}`]:J,[`${D}-${Q}`]:Q,[`${D}-rtl`]:p==="rtl"},u,e,n),d={},v=i[0]!=null&&i[0]>0?i[0]/-2:void 0;v&&(d.marginLeft=v,d.marginRight=v);const[S,o]=i;d.rowGap=o;const s=c.useMemo(()=>({gutter:[S,o],wrap:P}),[S,o,P]);return V(c.createElement(W.Z.Provider,{value:s},c.createElement("div",Object.assign({},j,{className:a,style:Object.assign(Object.assign({},d),g),ref:x}),O)))})},72269:function(Y,w,t){t.d(w,{Z:function(){return V}});var c=t(67294),M=t(19267),T=t(93967),z=t.n(T),Z=t(87462),b=t(4942),A=t(97685),W=t(91),B=t(21770),L=t(15105),$=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],U=c.forwardRef(function(e,n){var i,a=e.prefixCls,d=a===void 0?"rc-switch":a,v=e.className,S=e.checked,o=e.defaultChecked,s=e.disabled,f=e.loadingIcon,E=e.checkedChildren,N=e.unCheckedChildren,q=e.onClick,K=e.onChange,_=e.onKeyDown,re=(0,W.Z)(e,$),le=(0,B.Z)(!1,{value:S,defaultValue:o}),ae=(0,A.Z)(le,2),ee=ae[0],F=ae[1];function te(R,ie){var ne=ee;return s||(ne=R,F(ne),K==null||K(ne,ie)),ne}function oe(R){R.which===L.Z.LEFT?te(!1,R):R.which===L.Z.RIGHT&&te(!0,R),_==null||_(R)}function X(R){var ie=te(!ee,R);q==null||q(ie,R)}var se=z()(d,v,(i={},(0,b.Z)(i,"".concat(d,"-checked"),ee),(0,b.Z)(i,"".concat(d,"-disabled"),s),i));return c.createElement("button",(0,Z.Z)({},re,{type:"button",role:"switch","aria-checked":ee,disabled:s,className:se,ref:n,onKeyDown:oe,onClick:X}),f,c.createElement("span",{className:"".concat(d,"-inner")},c.createElement("span",{className:"".concat(d,"-inner-checked")},E),c.createElement("span",{className:"".concat(d,"-inner-unchecked")},N)))});U.displayName="Switch";var H=U,C=t(45353),I=t(53124),m=t(98866),x=t(98675),r=t(11568),l=t(15063),h=t(14747),u=t(83559),g=t(83262);const O=e=>{const{componentCls:n,trackHeightSM:i,trackPadding:a,trackMinWidthSM:d,innerMinMarginSM:v,innerMaxMarginSM:S,handleSizeSM:o,calc:s}=e,f=`${n}-inner`,E=(0,r.bf)(s(o).add(s(a).mul(2)).equal()),N=(0,r.bf)(s(S).mul(2).equal());return{[n]:{[`&${n}-small`]:{minWidth:d,height:i,lineHeight:(0,r.bf)(i),[`${n}-inner`]:{paddingInlineStart:S,paddingInlineEnd:v,[`${f}-checked, ${f}-unchecked`]:{minHeight:i},[`${f}-checked`]:{marginInlineStart:`calc(-100% + ${E} - ${N})`,marginInlineEnd:`calc(100% - ${E} + ${N})`},[`${f}-unchecked`]:{marginTop:s(i).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${n}-handle`]:{width:o,height:o},[`${n}-loading-icon`]:{top:s(s(o).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${n}-checked`]:{[`${n}-inner`]:{paddingInlineStart:v,paddingInlineEnd:S,[`${f}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${f}-unchecked`]:{marginInlineStart:`calc(100% - ${E} + ${N})`,marginInlineEnd:`calc(-100% + ${E} - ${N})`}},[`${n}-handle`]:{insetInlineStart:`calc(100% - ${(0,r.bf)(s(o).add(a).equal())})`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${f}`]:{[`${f}-unchecked`]:{marginInlineStart:s(e.marginXXS).div(2).equal(),marginInlineEnd:s(e.marginXXS).mul(-1).div(2).equal()}},[`&${n}-checked ${f}`]:{[`${f}-checked`]:{marginInlineStart:s(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:s(e.marginXXS).div(2).equal()}}}}}}},y=e=>{const{componentCls:n,handleSize:i,calc:a}=e;return{[n]:{[`${n}-loading-icon${e.iconCls}`]:{position:"relative",top:a(a(i).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${n}-checked ${n}-loading-icon`]:{color:e.switchColor}}}},P=e=>{const{componentCls:n,trackPadding:i,handleBg:a,handleShadow:d,handleSize:v,calc:S}=e,o=`${n}-handle`;return{[n]:{[o]:{position:"absolute",top:i,insetInlineStart:i,width:v,height:v,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:a,borderRadius:S(v).div(2).equal(),boxShadow:d,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${n}-checked ${o}`]:{insetInlineStart:`calc(100% - ${(0,r.bf)(S(v).add(i).equal())})`},[`&:not(${n}-disabled):active`]:{[`${o}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${n}-checked ${o}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},j=e=>{const{componentCls:n,trackHeight:i,trackPadding:a,innerMinMargin:d,innerMaxMargin:v,handleSize:S,calc:o}=e,s=`${n}-inner`,f=(0,r.bf)(o(S).add(o(a).mul(2)).equal()),E=(0,r.bf)(o(v).mul(2).equal());return{[n]:{[s]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:v,paddingInlineEnd:d,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${s}-checked, ${s}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:i},[`${s}-checked`]:{marginInlineStart:`calc(-100% + ${f} - ${E})`,marginInlineEnd:`calc(100% - ${f} + ${E})`},[`${s}-unchecked`]:{marginTop:o(i).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${n}-checked ${s}`]:{paddingInlineStart:d,paddingInlineEnd:v,[`${s}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${s}-unchecked`]:{marginInlineStart:`calc(100% - ${f} + ${E})`,marginInlineEnd:`calc(-100% + ${f} - ${E})`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${s}`]:{[`${s}-unchecked`]:{marginInlineStart:o(a).mul(2).equal(),marginInlineEnd:o(a).mul(-1).mul(2).equal()}},[`&${n}-checked ${s}`]:{[`${s}-checked`]:{marginInlineStart:o(a).mul(-1).mul(2).equal(),marginInlineEnd:o(a).mul(2).equal()}}}}}},G=e=>{const{componentCls:n,trackHeight:i,trackMinWidth:a}=e;return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,h.Wf)(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:a,height:i,lineHeight:(0,r.bf)(i),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${n}-disabled)`]:{background:e.colorTextTertiary}}),(0,h.Qy)(e)),{[`&${n}-checked`]:{background:e.switchColor,[`&:hover:not(${n}-disabled)`]:{background:e.colorPrimaryHover}},[`&${n}-loading, &${n}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${n}-rtl`]:{direction:"rtl"}})}},p=e=>{const{fontSize:n,lineHeight:i,controlHeight:a,colorWhite:d}=e,v=n*i,S=a/2,o=2,s=v-o*2,f=S-o*2;return{trackHeight:v,trackHeightSM:S,trackMinWidth:s*2+o*4,trackMinWidthSM:f*2+o*2,trackPadding:o,handleBg:d,handleSize:s,handleSizeSM:f,handleShadow:`0 2px 4px 0 ${new l.t("#00230b").setA(.2).toRgbString()}`,innerMinMargin:s/2,innerMaxMargin:s+o+o*2,innerMinMarginSM:f/2,innerMaxMarginSM:f+o+o*2}};var k=(0,u.I$)("Switch",e=>{const n=(0,g.IX)(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[G(n),j(n),P(n),y(n),O(n)]},p),Q=function(e,n){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&n.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,a=Object.getOwnPropertySymbols(e);d<a.length;d++)n.indexOf(a[d])<0&&Object.prototype.propertyIsEnumerable.call(e,a[d])&&(i[a[d]]=e[a[d]]);return i};const D=c.forwardRef((e,n)=>{const{prefixCls:i,size:a,disabled:d,loading:v,className:S,rootClassName:o,style:s,checked:f,value:E,defaultChecked:N,defaultValue:q,onChange:K}=e,_=Q(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[re,le]=(0,B.Z)(!1,{value:f!=null?f:E,defaultValue:N!=null?N:q}),{getPrefixCls:ae,direction:ee,switch:F}=c.useContext(I.E_),te=c.useContext(m.Z),oe=(d!=null?d:te)||v,X=ae("switch",i),se=c.createElement("div",{className:`${X}-handle`},v&&c.createElement(M.Z,{className:`${X}-loading-icon`})),[R,ie,ne]=k(X),de=(0,x.Z)(a),ue=z()(F==null?void 0:F.className,{[`${X}-small`]:de==="small",[`${X}-loading`]:v,[`${X}-rtl`]:ee==="rtl"},S,o,ie,ne),he=Object.assign(Object.assign({},F==null?void 0:F.style),s),fe=(...ce)=>{le(ce[0]),K==null||K.apply(void 0,ce)};return R(c.createElement(C.Z,{component:"Switch"},c.createElement(H,Object.assign({},_,{checked:re,onChange:fe,prefixCls:X,className:ue,style:he,disabled:oe,ref:n,loadingIcon:se}))))});D.__ANT_SWITCH=!0;var V=D}}]);
