#!/bin/bash

# 8088端口专用部署脚本
# 容器内nginx监听8088端口，直接映射到宿主机8088端口

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 8088端口容器化HTTPS环境部署 ===${NC}"

# 配置
SERVER_IP="*************"
HTTP_PORT="8088"
CONTAINER_NAME="xinhe-nginx-8088"
BACKEND_PORT="9021"
WEBSOCKET_PORT="8099"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker未安装，请先安装Docker${NC}"
    exit 1
fi

# 检查端口占用
echo -e "${YELLOW}检查端口占用...${NC}"
if netstat -tlnp 2>/dev/null | grep -q ":$HTTP_PORT "; then
    echo -e "${YELLOW}端口$HTTP_PORT已被占用，尝试停止现有容器...${NC}"
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    sleep 2
fi

# 步骤1：构建前端
echo -e "${BLUE}步骤1：构建前端应用${NC}"
if [ -d "web" ]; then
    cd web
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}安装前端依赖...${NC}"
        npm install
    fi
    
    # 构建
    echo -e "${YELLOW}构建生产版本...${NC}"
    npm run build
    
    if [ ! -d "dist" ]; then
        echo -e "${RED}构建失败：找不到dist目录${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}前端构建完成${NC}"
    cd ..
else
    echo -e "${RED}未找到web目录${NC}"
    exit 1
fi

# 步骤2：注入HTTPS标识
echo -e "${BLUE}步骤2：注入HTTPS环境标识${NC}"
if [ -f "inject-https-meta.js" ]; then
    node inject-https-meta.js
    echo -e "${GREEN}HTTPS标识注入完成${NC}"
else
    echo -e "${YELLOW}未找到inject-https-meta.js，跳过注入${NC}"
fi

# 步骤3：验证nginx配置
echo -e "${BLUE}步骤3：验证nginx配置${NC}"
if [ ! -f "nginx-container.conf" ]; then
    echo -e "${RED}未找到nginx-container.conf配置文件${NC}"
    exit 1
fi

# 检查配置中的端口
if ! grep -q "listen 8088" nginx-container.conf; then
    echo -e "${RED}nginx配置文件中未找到'listen 8088'，请检查配置${NC}"
    exit 1
fi

echo -e "${GREEN}nginx配置验证通过${NC}"

# 步骤4：启动容器
echo -e "${BLUE}步骤4：启动nginx容器${NC}"

# 停止现有容器
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# 启动新容器
echo -e "${YELLOW}启动容器...${NC}"
docker run -d \
  --name $CONTAINER_NAME \
  -p $HTTP_PORT:8088 \
  -v $(pwd)/nginx-container.conf:/etc/nginx/nginx.conf:ro \
  -v $(pwd)/web/dist:/usr/share/nginx/html:ro \
  --add-host host.docker.internal:host-gateway \
  --restart unless-stopped \
  nginx:alpine

# 等待容器启动
echo -e "${YELLOW}等待容器启动...${NC}"
sleep 10

# 步骤5：验证部署
echo -e "${BLUE}步骤5：验证部署${NC}"

# 检查容器状态
if docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${GREEN}✅ 容器启动成功${NC}"
else
    echo -e "${RED}❌ 容器启动失败${NC}"
    echo -e "${YELLOW}查看容器日志：${NC}"
    docker logs $CONTAINER_NAME
    exit 1
fi

# 检查nginx配置
echo -e "${YELLOW}检查nginx配置...${NC}"
if docker exec $CONTAINER_NAME nginx -t 2>/dev/null; then
    echo -e "${GREEN}✅ nginx配置正确${NC}"
else
    echo -e "${RED}❌ nginx配置错误${NC}"
    docker exec $CONTAINER_NAME nginx -t
    exit 1
fi

# 测试HTTP访问
echo -e "${YELLOW}测试HTTP访问...${NC}"
sleep 5
if curl -s -I "http://$SERVER_IP:$HTTP_PORT" | grep -q "200\|301\|302"; then
    echo -e "${GREEN}✅ HTTP访问正常${NC}"
else
    echo -e "${YELLOW}⚠️ HTTP访问异常，检查详细信息...${NC}"
    echo -e "${YELLOW}容器日志：${NC}"
    docker logs --tail=10 $CONTAINER_NAME
    echo -e "${YELLOW}curl测试：${NC}"
    curl -v "http://$SERVER_IP:$HTTP_PORT" || true
fi

# 检查HTTPS模拟头
echo -e "${YELLOW}检查HTTPS模拟头...${NC}"
HEADERS=$(curl -s -I "http://$SERVER_IP:$HTTP_PORT" 2>/dev/null || echo "")
if echo "$HEADERS" | grep -q "X-Forwarded-Proto.*https"; then
    echo -e "${GREEN}✅ HTTPS模拟头配置正确${NC}"
else
    echo -e "${YELLOW}⚠️ 未检测到HTTPS模拟头${NC}"
    echo -e "${YELLOW}响应头信息：${NC}"
    echo "$HEADERS"
fi

echo -e "${GREEN}=== 部署完成 ===${NC}"
echo -e "${YELLOW}访问信息：${NC}"
echo "  🌐 应用地址: http://$SERVER_IP:$HTTP_PORT"
echo "  🐳 容器名称: $CONTAINER_NAME"
echo "  🔌 端口映射: $HTTP_PORT:8088"
echo "  🔒 HTTPS模拟: 已启用"
echo ""
echo -e "${YELLOW}管理命令：${NC}"
echo "  查看状态: docker ps | grep $CONTAINER_NAME"
echo "  查看日志: docker logs -f $CONTAINER_NAME"
echo "  重启容器: docker restart $CONTAINER_NAME"
echo "  停止容器: docker stop $CONTAINER_NAME"
echo "  进入容器: docker exec -it $CONTAINER_NAME sh"
echo "  测试配置: docker exec $CONTAINER_NAME nginx -t"
echo ""
echo -e "${BLUE}重要提醒：${NC}"
echo "1. 🔐 Web Crypto API应该在此配置下可用"
echo "2. 🔧 请更新Keycloak客户端配置："
echo "   - 重定向URI: http://$SERVER_IP:$HTTP_PORT/auth/callback"
echo "   - Web Origins: http://$SERVER_IP:$HTTP_PORT"
echo "3. 🧪 在浏览器中访问 http://$SERVER_IP:$HTTP_PORT 并点击'环境诊断'"
echo "4. 🔍 如有问题，查看容器日志进行调试"

echo -e "${GREEN}🎉 8088端口部署完成！${NC}"
