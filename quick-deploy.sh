#!/bin/bash

# 快速部署脚本 - 容器化HTTPS环境
# 使用HTTP端口但通过X-Forwarded-Proto模拟HTTPS

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 快速部署到容器化HTTPS环境 ===${NC}"

# 配置
SERVER_IP="*************"
HTTP_PORT="8088"
CONTAINER_NAME="xinhe-nginx-https"

# 步骤1：构建前端
echo -e "${BLUE}步骤1：构建前端应用${NC}"
cd web
npm run build
cd ..

# 步骤2：注入HTTPS标识
echo -e "${BLUE}步骤2：注入HTTPS环境标识${NC}"
node inject-https-meta.js

# 步骤3：停止现有容器
echo -e "${BLUE}步骤3：停止现有容器${NC}"
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# 步骤4：启动新容器
echo -e "${BLUE}步骤4：启动nginx容器${NC}"
docker run -d \
  --name $CONTAINER_NAME \
  -p $HTTP_PORT:8088 \
  -v $(pwd)/nginx-container.conf:/etc/nginx/nginx.conf:ro \
  -v $(pwd)/web/dist:/usr/share/nginx/html:ro \
  --add-host host.docker.internal:host-gateway \
  nginx:alpine

# 等待容器启动
echo -e "${YELLOW}等待容器启动...${NC}"
sleep 5

# 验证部署
echo -e "${BLUE}步骤5：验证部署${NC}"
if docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${GREEN}✅ 容器启动成功${NC}"
    
    # 测试访问
    if curl -s -I "http://$SERVER_IP:$HTTP_PORT" | grep -q "200\|301\|302"; then
        echo -e "${GREEN}✅ 应用访问正常${NC}"
    else
        echo -e "${YELLOW}⚠️ 应用访问需要检查${NC}"
    fi
else
    echo -e "${RED}❌ 容器启动失败${NC}"
    docker logs $CONTAINER_NAME
    exit 1
fi

echo -e "${GREEN}=== 部署完成 ===${NC}"
echo -e "${YELLOW}访问信息：${NC}"
echo "  🌐 应用地址: http://$SERVER_IP:$HTTP_PORT"
echo "  🐳 容器名称: $CONTAINER_NAME"
echo "  🔒 HTTPS模拟: 已启用"
echo ""
echo -e "${YELLOW}管理命令：${NC}"
echo "  查看日志: docker logs -f $CONTAINER_NAME"
echo "  重启容器: docker restart $CONTAINER_NAME"
echo "  停止容器: docker stop $CONTAINER_NAME"
echo "  进入容器: docker exec -it $CONTAINER_NAME sh"
echo ""
echo -e "${BLUE}重要提醒：${NC}"
echo "1. 🔐 Web Crypto API应该在此配置下可用"
echo "2. 🔧 请更新Keycloak客户端配置："
echo "   - 重定向URI: http://$SERVER_IP:$HTTP_PORT/auth/callback"
echo "   - Web Origins: http://$SERVER_IP:$HTTP_PORT"
echo "3. 🧪 在浏览器中点击'环境诊断'验证配置"
