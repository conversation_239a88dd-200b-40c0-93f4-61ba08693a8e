"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5744],{47046:function(K,b){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};b.Z=e},49495:function(K,b){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};b.Z=e},39055:function(K,b){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};b.Z=e},85175:function(K,b,e){var a=e(1413),c=e(67294),O=e(48820),B=e(91146),S=function(p,C){return c.createElement(B.Z,(0,a.Z)((0,a.Z)({},p),{},{ref:C,icon:O.Z}))},h=c.forwardRef(S);b.Z=h},82061:function(K,b,e){var a=e(1413),c=e(67294),O=e(47046),B=e(91146),S=function(p,C){return c.createElement(B.Z,(0,a.Z)((0,a.Z)({},p),{},{ref:C,icon:O.Z}))},h=c.forwardRef(S);b.Z=h},50336:function(K,b,e){e.d(b,{Z:function(){return p}});var a=e(1413),c=e(67294),O={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M476 399.1c0-3.9-3.1-7.1-7-7.1h-42c-3.8 0-7 3.2-7 7.1V484h-84.5c-4.1 0-7.5 3.1-7.5 7v42c0 3.8 3.4 7 7.5 7H420v84.9c0 3.9 3.2 7.1 7 7.1h42c3.9 0 7-3.2 7-7.1V540h84.5c4.1 0 7.5-3.2 7.5-7v-42c0-3.9-3.4-7-7.5-7H476v-84.9zM560.5 704h-225c-4.1 0-7.5 3.2-7.5 7v42c0 3.8 3.4 7 7.5 7h225c4.1 0 7.5-3.2 7.5-7v-42c0-3.8-3.4-7-7.5-7zm-7.1-502.6c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v704c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V397.3c0-8.5-3.4-16.6-9.4-22.6L553.4 201.4zM664 888H232V264h282.2L664 413.8V888zm190.2-581.4L611.3 72.9c-6-5.7-13.9-8.9-22.2-8.9H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h277l219 210.6V824c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V329.6c0-8.7-3.5-17-9.8-23z"}}]},name:"diff",theme:"outlined"},B=O,S=e(91146),h=function(ee,oe){return c.createElement(S.Z,(0,a.Z)((0,a.Z)({},ee),{},{ref:oe,icon:B}))},A=c.forwardRef(h),p=A},34804:function(K,b,e){var a=e(1413),c=e(67294),O=e(66023),B=e(91146),S=function(p,C){return c.createElement(B.Z,(0,a.Z)((0,a.Z)({},p),{},{ref:C,icon:O.Z}))},h=c.forwardRef(S);b.Z=h},69753:function(K,b,e){var a=e(1413),c=e(67294),O=e(49495),B=e(91146),S=function(p,C){return c.createElement(B.Z,(0,a.Z)((0,a.Z)({},p),{},{ref:C,icon:O.Z}))},h=c.forwardRef(S);b.Z=h},51042:function(K,b,e){var a=e(1413),c=e(67294),O=e(42110),B=e(91146),S=function(p,C){return c.createElement(B.Z,(0,a.Z)((0,a.Z)({},p),{},{ref:C,icon:O.Z}))},h=c.forwardRef(S);b.Z=h},40110:function(K,b,e){var a=e(1413),c=e(67294),O=e(509),B=e(91146),S=function(p,C){return c.createElement(B.Z,(0,a.Z)((0,a.Z)({},p),{},{ref:C,icon:O.Z}))},h=c.forwardRef(S);b.Z=h},41441:function(K,b,e){var a=e(1413),c=e(67294),O=e(39055),B=e(91146),S=function(p,C){return c.createElement(B.Z,(0,a.Z)((0,a.Z)({},p),{},{ref:C,icon:O.Z}))},h=c.forwardRef(S);b.Z=h},7569:function(K,b,e){e.r(b),e.d(b,{default:function(){return le}});var a=e(15009),c=e.n(a),O=e(19632),B=e.n(O),S=e(99289),h=e.n(S),A=e(5574),p=e.n(A),C=e(7528),ee=e(40110),oe=e(82061),fe=e(51042),ve=e(84017),re=e(11941),Y=e(47019),I=e(2453),J=e(17788),f=e(55102),u=e(83622),w=e(78957),E=e(96074),D=e(2487),j=e(67294),H=e(20057),X=e(85425),P=e(57945),se=e(44394),n=e(85893),k=re.Z.TabPane,le=function(){var Z=(0,H.TH)(),r=(0,H.s0)(),g=(0,j.useState)(void 0),y=p()(g,2),t=y[0],o=y[1],x=(0,j.useState)("tab1"),F=p()(x,2),R=F[0],W=F[1],Q=(0,j.useState)([]),L=p()(Q,2),V=L[0],z=L[1],ne=(0,j.useState)([]),te=p()(ne,2),$=te[0],G=te[1];(0,j.useEffect)(function(){var m=function(){var i=h()(c()().mark(function l(){var d,s,v;return c()().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:if(!(Z.state&&Z.state.proId)){T.next=4;break}o(Z.state.proId),T.next=14;break;case 4:return T.prev=4,T.next=7,(0,C.hW)();case 7:d=T.sent,d&&d.length>0&&(s=B()(d).sort(function(De,Oe){var Se=new Date(De.last_activity_at||0),Pe=new Date(Oe.last_activity_at||0);return Pe.getTime()-Se.getTime()}),v=s[0],o(v.id)),T.next=14;break;case 11:T.prev=11,T.t0=T.catch(4),console.error("Failed to fetch project information:",T.t0);case 14:case"end":return T.stop()}},l,null,[[4,11]])}));return function(){return i.apply(this,arguments)}}();m()},[Z.state]);var ie=(0,j.useState)(!1),je=p()(ie,2),Me=je[0],ce=je[1],he=(0,j.useState)(""),Ze=p()(he,2),N=Ze[0],Ke=Ze[1],He=(0,j.useState)(!1),Fe=p()(He,2),Ve=Fe[0],ue=Fe[1],Ge=(0,j.useState)(!1),Te=p()(Ge,2),xe=Te[0],de=Te[1],Re=Y.Z.useForm(),ae=p()(Re,1),ze=ae[0];(0,j.useEffect)(function(){var m=function(){var i=h()(c()().mark(function l(){var d,s;return c()().wrap(function(M){for(;;)switch(M.prev=M.next){case 0:if(t){M.next=2;break}return M.abrupt("return");case 2:return d={id:t},M.prev=3,M.next=6,(0,C.$y)(d);case 6:s=M.sent,z(s),G(s),M.next=14;break;case 11:M.prev=11,M.t0=M.catch(3),console.error("Failed to fetch branch:",M.t0);case 14:case"end":return M.stop()}},l,null,[[3,11]])}));return function(){return i.apply(this,arguments)}}();m()},[t]);var _=(0,j.useState)(!1),me=p()(_,2),ge=me[0],Ye=me[1],_e=function(){var m=h()(c()().mark(function i(l){var d;return c()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:if(t){v.next=3;break}return I.ZP.error("\u9879\u76EEID\u4E0D\u5B58\u5728"),v.abrupt("return");case 3:if(l.name){v.next=6;break}return I.ZP.error("\u8BF7\u8F93\u5165\u5206\u652F\u540D\u79F0"),v.abrupt("return");case 6:if(l.ref){v.next=9;break}return I.ZP.error("\u8BF7\u8F93\u5165\u521B\u5EFA\u6765\u6E90"),v.abrupt("return");case 9:return v.prev=9,Ye(!0),v.next=13,(0,C.Qj)({id:t,name:l.name,ref:l.ref});case 13:return I.ZP.success("\u5206\u652F\u521B\u5EFA\u6210\u529F"),de(!1),ze.resetFields(),v.next=18,(0,C.$y)({id:t});case 18:d=v.sent,z(d),G(d),v.next=27;break;case 23:v.prev=23,v.t0=v.catch(9),console.error("\u521B\u5EFA\u5206\u652F\u5931\u8D25:",v.t0),I.ZP.error("\u521B\u5EFA\u5206\u652F\u5931\u8D25: ".concat((v.t0===null||v.t0===void 0?void 0:v.t0.message)||"\u672A\u77E5\u9519\u8BEF"));case 27:return v.prev=27,Ye(!1),v.finish(27);case 30:case"end":return v.stop()}},i,null,[[9,23,27,30]])}));return function(l){return m.apply(this,arguments)}}(),pe=function(i){var l=$.find(function(d){return d.name===i});if(l!=null&&l.protected){I.ZP.warning("\u53D7\u4FDD\u62A4\u5206\u652F\u4E0D\u80FD\u5220\u9664");return}Ke(i),ce(!0)},qe=function(){var m=h()(c()().mark(function i(){var l;return c()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(t){s.next=3;break}return I.ZP.error("\u9879\u76EEID\u4E0D\u5B58\u5728"),s.abrupt("return");case 3:return s.prev=3,s.next=6,(0,C.G8)({id:t,name:N});case 6:return I.ZP.success("\u5206\u652F\u5220\u9664\u6210\u529F"),ce(!1),s.next=10,(0,C.$y)({id:t});case 10:l=s.sent,z(l),G(l),s.next=19;break;case 15:s.prev=15,s.t0=s.catch(3),I.ZP.error("\u5220\u9664\u5206\u652F\u5931\u8D25"),console.error("Failed to delete branch:",s.t0);case 19:case"end":return s.stop()}},i,null,[[3,15]])}));return function(){return m.apply(this,arguments)}}(),er=function(){var m=h()(c()().mark(function i(){var l;return c()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(t){s.next=3;break}return I.ZP.error("\u9879\u76EEID\u4E0D\u5B58\u5728"),s.abrupt("return");case 3:return s.prev=3,s.next=6,(0,C.Zo)({id:t});case 6:return I.ZP.success("\u5DF2\u5408\u5E76\u5206\u652F\u5220\u9664\u6210\u529F"),ue(!1),s.next=10,(0,C.$y)({id:t});case 10:l=s.sent,z(l),G(l),s.next=19;break;case 15:s.prev=15,s.t0=s.catch(3),I.ZP.error("\u5220\u9664\u5DF2\u5408\u5E76\u5206\u652F\u5931\u8D25"),console.error("Failed to delete merged branches:",s.t0);case 19:case"end":return s.stop()}},i,null,[[3,15]])}));return function(){return m.apply(this,arguments)}}(),rr=function(i){if(i){var l=V.filter(function(d){return d.name.toLowerCase().includes(i.toLowerCase())});G(l)}else G(V)},Ce=function(i){navigator.clipboard.writeText(i).then(function(){I.ZP.success("\u5DF2\u590D\u5236\u5206\u652F\u540D\u79F0: ".concat(i))}).catch(function(l){console.error("Failed to copy branch name:",l),I.ZP.error("\u590D\u5236\u5931\u8D25")})},be=function(){var m=h()(c()().mark(function i(l){var d,s,v,M,T,De,Oe,Se,Pe,Be,$e,Ie,We,Le,Ne,Ue,ke,q,Xe;return c()().wrap(function(U){for(;;)switch(U.prev=U.next){case 0:if(Pe=$.find(function(Qe){return Qe.name===l}),Pe){U.next=4;break}return I.ZP.error("\u627E\u4E0D\u5230\u5206\u652F: ".concat(l)),U.abrupt("return");case 4:if(Be=(d=Z.state)===null||d===void 0?void 0:d.proName,$e=(s=Z.state)===null||s===void 0?void 0:s.ownerName,Ie=(v=Z.state)===null||v===void 0?void 0:v.visibility,We=(M=Z.state)===null||M===void 0?void 0:M.starCount,Le=(T=Z.state)===null||T===void 0?void 0:T.forkCount,Ne=(De=Z.state)===null||De===void 0?void 0:De.sshUrl,Ue=(Oe=Z.state)===null||Oe===void 0?void 0:Oe.httpUrl,!(!Be||!$e||!Ie)){U.next=23;break}return U.prev=12,U.next=15,(0,C.hW)();case 15:ke=U.sent,q=ke.find(function(Qe){return Qe.id===t}),q&&(Be=Be||q.name,$e=$e||((Xe=q.owner)===null||Xe===void 0?void 0:Xe.name),Ie=Ie||q.visibility,We=We||q.star_count,Le=Le||q.forks_count,Ne=Ne||q.ssh_url_to_repo,Ue=Ue||q.http_url_to_repo),U.next=23;break;case 20:U.prev=20,U.t0=U.catch(12),console.error("Failed to fetch project details:",U.t0);case 23:console.log("\u5C06\u8DF3\u8F6C\u5230files\u9875\u9762\uFF0C\u5E76\u4F7F\u7528\u5206\u652F: ".concat(l)),r("/tools/repo/files",{state:{proId:t,defaultBranch:l,proName:Be||"\u9879\u76EE",ownerName:$e||"\u7528\u6237",update:(Se=Pe.commit)===null||Se===void 0?void 0:Se.created_at,visibility:Ie||"private",starCount:We||0,forkCount:Le||0,sshUrl:Ne,httpUrl:Ue},replace:!0});case 25:case"end":return U.stop()}},i,null,[[12,20]])}));return function(l){return m.apply(this,arguments)}}(),Ee=function(i){var l,d,s=$.find(function(M){return M.default===!0}),v=s?s.name:"main";r("/tools/repo/compare/compare_result",{state:{sourceProjectId:t,targetProjectId:t,sourceProject:((l=Z.state)===null||l===void 0?void 0:l.proName)||"\u9879\u76EE",targetProject:((d=Z.state)===null||d===void 0?void 0:d.proName)||"\u9879\u76EE",sourceBranch:i,targetBranch:v}})},ye=function(i){var l,d=$.find(function(v){return v.default===!0}),s=d?d.name:"main";r("/tools/repo/newMergeRequest",{state:{proId:t,proName:((l=Z.state)===null||l===void 0?void 0:l.proName)||"\u9879\u76EE",sourceBranch:i,targetBranch:s,check:"mergeRequests"}})},Je=(0,j.useMemo)(function(){var m=$.filter(function(l){var d;return(0,se.E_)((d=l.commit)===null||d===void 0?void 0:d.created_at)}),i=$.filter(function(l){var d;return!(0,se.E_)((d=l.commit)===null||d===void 0?void 0:d.created_at)});return{activeBranches:m.map(P.y_),inactiveBranches:i.map(P.y_)}},[$]),Ae=Je.activeBranches,we=Je.inactiveBranches,nr=(0,j.useMemo)(function(){return Ae.slice(0,5)},[Ae]),tr=(0,j.useMemo)(function(){return we.slice(0,5)},[we]);return(0,n.jsxs)(ve._z,{header:{title:"",breadcrumb:{}},children:[(0,n.jsx)(X.Z,{customItems:[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801"},{path:"/tools/repo/branch",breadcrumbName:"\u5206\u652F\u7BA1\u7406"}]}),(0,n.jsxs)(J.Z,{title:"\u5220\u9664\u5206\u652F",open:Me,onOk:qe,onCancel:function(){return ce(!1)},okText:"\u786E\u8BA4\u5220\u9664",cancelText:"\u53D6\u6D88",children:[(0,n.jsxs)("p",{children:['\u786E\u5B9A\u8981\u5220\u9664\u5206\u652F "',N,'" \u5417\uFF1F']}),(0,n.jsx)("p",{children:"\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\u3002"})]}),(0,n.jsxs)(J.Z,{title:"\u5220\u9664\u5DF2\u5408\u5E76\u5206\u652F",open:Ve,onOk:er,onCancel:function(){return ue(!1)},okText:"\u786E\u8BA4\u5220\u9664",cancelText:"\u53D6\u6D88",children:[(0,n.jsx)("p",{children:"\u786E\u5B9A\u8981\u5220\u9664\u6240\u6709\u5DF2\u5408\u5E76\u5206\u652F\u5417\uFF1F"}),(0,n.jsx)("p",{children:"\u6B64\u64CD\u4F5C\u5C06\u5220\u9664\u6240\u6709\u5DF2\u5408\u5E76\u5230\u9ED8\u8BA4\u5206\u652F\u7684\u5206\u652F\uFF0C\u4E14\u4E0D\u53EF\u6062\u590D\u3002"})]}),(0,n.jsx)(J.Z,{title:"\u65B0\u5EFA\u5206\u652F",open:xe,onCancel:function(){return de(!1)},footer:null,children:(0,n.jsxs)(Y.Z,{form:ze,layout:"vertical",onFinish:_e,children:[(0,n.jsx)(Y.Z.Item,{name:"name",label:"\u5206\u652F\u540D\u79F0",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u5206\u652F\u540D\u79F0"}],children:(0,n.jsx)(f.Z,{placeholder:"\u8F93\u5165\u5206\u652F\u540D\u79F0"})}),(0,n.jsx)(Y.Z.Item,{name:"ref",label:"\u521B\u5EFA\u81EA",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u521B\u5EFA\u6765\u6E90"}],children:(0,n.jsx)(f.Z,{placeholder:"\u8F93\u5165\u521B\u5EFA\u6765\u6E90\uFF08\u5206\u652F\u540D\u6216\u63D0\u4EA4SHA\uFF09"})}),(0,n.jsxs)(Y.Z.Item,{children:[(0,n.jsx)(u.ZP,{type:"primary",htmlType:"submit",style:{marginRight:8},loading:ge,children:"\u521B\u5EFA"}),(0,n.jsx)(u.ZP,{onClick:function(){return de(!1)},children:"\u53D6\u6D88"})]})]})}),(0,n.jsxs)("div",{style:{padding:"0",borderRadius:"4px"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"0 16px"},children:[(0,n.jsx)("div",{style:{display:"flex",flex:1},children:(0,n.jsxs)(re.Z,{activeKey:R,onChange:function(i){return W(i)},style:{width:"100%"},children:[(0,n.jsx)(k,{tab:"\u6982\u89C8"},"tab1"),(0,n.jsx)(k,{tab:"\u6D3B\u8DC3"},"tab2"),(0,n.jsx)(k,{tab:"\u975E\u6D3B\u8DC3"},"tab3"),(0,n.jsx)(k,{tab:"\u5168\u90E8"},"tab4")]})}),(0,n.jsx)("div",{children:(0,n.jsxs)(w.Z,{size:12,children:[(0,n.jsx)(f.Z,{placeholder:"\u641C\u7D22\u5206\u652F",prefix:(0,n.jsx)(ee.Z,{}),style:{width:200},onChange:function(i){return rr(i.target.value)},allowClear:!0}),(0,n.jsx)(u.ZP,{icon:(0,n.jsx)(oe.Z,{}),danger:!0,onClick:function(){return ue(!0)},children:"\u5220\u9664\u5DF2\u5408\u5E76\u5206\u652F"}),(0,n.jsx)(u.ZP,{type:"primary",icon:(0,n.jsx)(fe.Z,{}),style:{background:"#007bff"},onClick:function(){return de(!0)},children:"\u65B0\u5EFA\u5206\u652F"})]})})]}),(0,n.jsxs)("div",{style:{padding:"0 16px"},children:[R==="tab1"&&(0,n.jsxs)(n.Fragment,{children:[Ae.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h3",{children:"\u6D3B\u8DC3\u5206\u652F"}),(0,n.jsx)(E.Z,{style:{margin:0}}),(0,n.jsx)(D.Z,{size:"large",rowKey:"id",dataSource:nr,renderItem:function(i){return(0,n.jsx)(P.$E,{branches:[i],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:Ee,onMergeRequest:ye})}}),(0,n.jsx)(E.Z,{style:{margin:0}}),(0,n.jsx)("div",{style:{marginTop:"12px"},children:(0,n.jsx)("a",{onClick:function(){return W("tab2")},style:{cursor:"pointer"},children:"\u67E5\u770B\u66F4\u591A\u6D3B\u8DC3\u5206\u652F"})}),(0,n.jsx)(E.Z,{})]}),we.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h3",{children:"\u975E\u6D3B\u8DC3\u5206\u652F"}),(0,n.jsx)(E.Z,{style:{margin:0}}),(0,n.jsx)(D.Z,{size:"large",rowKey:"name",dataSource:tr,renderItem:function(i){return(0,n.jsx)(P.$E,{branches:[i],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:Ee,onMergeRequest:ye})}}),(0,n.jsx)(E.Z,{style:{margin:0}}),(0,n.jsx)("div",{style:{margin:"12px 0"},children:(0,n.jsx)("a",{onClick:function(){return W("tab3")},style:{cursor:"pointer"},children:"\u67E5\u770B\u66F4\u591A\u975E\u6D3B\u8DC3\u5206\u652F"})})]})]}),R==="tab2"&&(0,n.jsx)(D.Z,{size:"large",rowKey:"name",dataSource:Ae,renderItem:function(i){return(0,n.jsx)(P.$E,{branches:[i],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:Ee,onMergeRequest:ye})}}),R==="tab3"&&(0,n.jsx)(D.Z,{size:"large",rowKey:"name",dataSource:we,renderItem:function(i){return(0,n.jsx)(P.$E,{branches:[i],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:Ee,onMergeRequest:ye})}}),R==="tab4"&&(0,n.jsx)(D.Z,{size:"large",rowKey:"name",dataSource:$.map(P.y_),renderItem:function(i){return(0,n.jsx)(P.$E,{branches:[i],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:Ee,onMergeRequest:ye})}})]})]})]})}},96074:function(K,b,e){e.d(b,{Z:function(){return J}});var a=e(67294),c=e(93967),O=e.n(c),B=e(53124),S=e(98675),h=e(11568),A=e(14747),p=e(83559),C=e(83262);const ee=f=>{const{componentCls:u}=f;return{[u]:{"&-horizontal":{[`&${u}`]:{"&-sm":{marginBlock:f.marginXS},"&-md":{marginBlock:f.margin}}}}}},oe=f=>{const{componentCls:u,sizePaddingEdgeHorizontal:w,colorSplit:E,lineWidth:D,textPaddingInline:j,orientationMargin:H,verticalMarginInline:X}=f;return{[u]:Object.assign(Object.assign({},(0,A.Wf)(f)),{borderBlockStart:`${(0,h.bf)(D)} solid ${E}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:X,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,h.bf)(D)} solid ${E}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,h.bf)(f.marginLG)} 0`},[`&-horizontal${u}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,h.bf)(f.dividerHorizontalWithTextGutterMargin)} 0`,color:f.colorTextHeading,fontWeight:500,fontSize:f.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${E}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,h.bf)(D)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${u}-with-text-start`]:{"&::before":{width:`calc(${H} * 100%)`},"&::after":{width:`calc(100% - ${H} * 100%)`}},[`&-horizontal${u}-with-text-end`]:{"&::before":{width:`calc(100% - ${H} * 100%)`},"&::after":{width:`calc(${H} * 100%)`}},[`${u}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:j},"&-dashed":{background:"none",borderColor:E,borderStyle:"dashed",borderWidth:`${(0,h.bf)(D)} 0 0`},[`&-horizontal${u}-with-text${u}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${u}-dashed`]:{borderInlineStartWidth:D,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:E,borderStyle:"dotted",borderWidth:`${(0,h.bf)(D)} 0 0`},[`&-horizontal${u}-with-text${u}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${u}-dotted`]:{borderInlineStartWidth:D,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${u}-with-text`]:{color:f.colorText,fontWeight:"normal",fontSize:f.fontSize},[`&-horizontal${u}-with-text-start${u}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${u}-inner-text`]:{paddingInlineStart:w}},[`&-horizontal${u}-with-text-end${u}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${u}-inner-text`]:{paddingInlineEnd:w}}})}},fe=f=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:f.marginXS});var ve=(0,p.I$)("Divider",f=>{const u=(0,C.IX)(f,{dividerHorizontalWithTextGutterMargin:f.margin,sizePaddingEdgeHorizontal:0});return[oe(u),ee(u)]},fe,{unitless:{orientationMargin:!0}}),re=function(f,u){var w={};for(var E in f)Object.prototype.hasOwnProperty.call(f,E)&&u.indexOf(E)<0&&(w[E]=f[E]);if(f!=null&&typeof Object.getOwnPropertySymbols=="function")for(var D=0,E=Object.getOwnPropertySymbols(f);D<E.length;D++)u.indexOf(E[D])<0&&Object.prototype.propertyIsEnumerable.call(f,E[D])&&(w[E[D]]=f[E[D]]);return w};const Y={small:"sm",middle:"md"};var J=f=>{const{getPrefixCls:u,direction:w,className:E,style:D}=(0,B.dj)("divider"),{prefixCls:j,type:H="horizontal",orientation:X="center",orientationMargin:P,className:se,rootClassName:n,children:k,dashed:le,variant:Z="solid",plain:r,style:g,size:y}=f,t=re(f,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),o=u("divider",j),[x,F,R]=ve(o),W=(0,S.Z)(y),Q=Y[W],L=!!k,V=a.useMemo(()=>X==="left"?w==="rtl"?"end":"start":X==="right"?w==="rtl"?"start":"end":X,[w,X]),z=V==="start"&&P!=null,ne=V==="end"&&P!=null,te=O()(o,E,F,R,`${o}-${H}`,{[`${o}-with-text`]:L,[`${o}-with-text-${V}`]:L,[`${o}-dashed`]:!!le,[`${o}-${Z}`]:Z!=="solid",[`${o}-plain`]:!!r,[`${o}-rtl`]:w==="rtl",[`${o}-no-default-orientation-margin-start`]:z,[`${o}-no-default-orientation-margin-end`]:ne,[`${o}-${Q}`]:!!Q},se,n),$=a.useMemo(()=>typeof P=="number"?P:/^\d+$/.test(P)?Number(P):P,[P]),G={marginInlineStart:z?$:void 0,marginInlineEnd:ne?$:void 0};return x(a.createElement("div",Object.assign({className:te,style:Object.assign(Object.assign({},D),g)},t,{role:"separator"}),k&&H!=="vertical"&&a.createElement("span",{className:`${o}-inner-text`,style:G},k)))}},66309:function(K,b,e){e.d(b,{Z:function(){return Z}});var a=e(67294),c=e(93967),O=e.n(c),B=e(98423),S=e(98787),h=e(69760),A=e(96159),p=e(45353),C=e(53124),ee=e(11568),oe=e(15063),fe=e(14747),ve=e(83262),re=e(83559);const Y=r=>{const{paddingXXS:g,lineWidth:y,tagPaddingHorizontal:t,componentCls:o,calc:x}=r,F=x(t).sub(y).equal(),R=x(g).sub(y).equal();return{[o]:Object.assign(Object.assign({},(0,fe.Wf)(r)),{display:"inline-block",height:"auto",marginInlineEnd:r.marginXS,paddingInline:F,fontSize:r.tagFontSize,lineHeight:r.tagLineHeight,whiteSpace:"nowrap",background:r.defaultBg,border:`${(0,ee.bf)(r.lineWidth)} ${r.lineType} ${r.colorBorder}`,borderRadius:r.borderRadiusSM,opacity:1,transition:`all ${r.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:r.defaultColor},[`${o}-close-icon`]:{marginInlineStart:R,fontSize:r.tagIconSize,color:r.colorIcon,cursor:"pointer",transition:`all ${r.motionDurationMid}`,"&:hover":{color:r.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${r.iconCls}-close, ${r.iconCls}-close:hover`]:{color:r.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:r.colorPrimary,backgroundColor:r.colorFillSecondary},"&:active, &-checked":{color:r.colorTextLightSolid},"&-checked":{backgroundColor:r.colorPrimary,"&:hover":{backgroundColor:r.colorPrimaryHover}},"&:active":{backgroundColor:r.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${r.iconCls} + span, > span + ${r.iconCls}`]:{marginInlineStart:F}}),[`${o}-borderless`]:{borderColor:"transparent",background:r.tagBorderlessBg}}},I=r=>{const{lineWidth:g,fontSizeIcon:y,calc:t}=r,o=r.fontSizeSM;return(0,ve.IX)(r,{tagFontSize:o,tagLineHeight:(0,ee.bf)(t(r.lineHeightSM).mul(o).equal()),tagIconSize:t(y).sub(t(g).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:r.defaultBg})},J=r=>({defaultBg:new oe.t(r.colorFillQuaternary).onBackground(r.colorBgContainer).toHexString(),defaultColor:r.colorText});var f=(0,re.I$)("Tag",r=>{const g=I(r);return Y(g)},J),u=function(r,g){var y={};for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&g.indexOf(t)<0&&(y[t]=r[t]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(r);o<t.length;o++)g.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(r,t[o])&&(y[t[o]]=r[t[o]]);return y},E=a.forwardRef((r,g)=>{const{prefixCls:y,style:t,className:o,checked:x,onChange:F,onClick:R}=r,W=u(r,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:Q,tag:L}=a.useContext(C.E_),V=ie=>{F==null||F(!x),R==null||R(ie)},z=Q("tag",y),[ne,te,$]=f(z),G=O()(z,`${z}-checkable`,{[`${z}-checkable-checked`]:x},L==null?void 0:L.className,o,te,$);return ne(a.createElement("span",Object.assign({},W,{ref:g,style:Object.assign(Object.assign({},t),L==null?void 0:L.style),className:G,onClick:V})))}),D=e(98719);const j=r=>(0,D.Z)(r,(g,{textColor:y,lightBorderColor:t,lightColor:o,darkColor:x})=>({[`${r.componentCls}${r.componentCls}-${g}`]:{color:y,background:o,borderColor:t,"&-inverse":{color:r.colorTextLightSolid,background:x,borderColor:x},[`&${r.componentCls}-borderless`]:{borderColor:"transparent"}}}));var H=(0,re.bk)(["Tag","preset"],r=>{const g=I(r);return j(g)},J);function X(r){return typeof r!="string"?r:r.charAt(0).toUpperCase()+r.slice(1)}const P=(r,g,y)=>{const t=X(y);return{[`${r.componentCls}${r.componentCls}-${g}`]:{color:r[`color${y}`],background:r[`color${t}Bg`],borderColor:r[`color${t}Border`],[`&${r.componentCls}-borderless`]:{borderColor:"transparent"}}}};var se=(0,re.bk)(["Tag","status"],r=>{const g=I(r);return[P(g,"success","Success"),P(g,"processing","Info"),P(g,"error","Error"),P(g,"warning","Warning")]},J),n=function(r,g){var y={};for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&g.indexOf(t)<0&&(y[t]=r[t]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(r);o<t.length;o++)g.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(r,t[o])&&(y[t[o]]=r[t[o]]);return y};const le=a.forwardRef((r,g)=>{const{prefixCls:y,className:t,rootClassName:o,style:x,children:F,icon:R,color:W,onClose:Q,bordered:L=!0,visible:V}=r,z=n(r,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:ne,direction:te,tag:$}=a.useContext(C.E_),[G,ie]=a.useState(!0),je=(0,B.Z)(z,["closeIcon","closable"]);a.useEffect(()=>{V!==void 0&&ie(V)},[V]);const Me=(0,S.o2)(W),ce=(0,S.yT)(W),he=Me||ce,Ze=Object.assign(Object.assign({backgroundColor:W&&!he?W:void 0},$==null?void 0:$.style),x),N=ne("tag",y),[Ke,He,Fe]=f(N),Ve=O()(N,$==null?void 0:$.className,{[`${N}-${W}`]:he,[`${N}-has-color`]:W&&!he,[`${N}-hidden`]:!G,[`${N}-rtl`]:te==="rtl",[`${N}-borderless`]:!L},t,o,He,Fe),ue=ae=>{ae.stopPropagation(),Q==null||Q(ae),!ae.defaultPrevented&&ie(!1)},[,Ge]=(0,h.Z)((0,h.w)(r),(0,h.w)($),{closable:!1,closeIconRender:ae=>{const ze=a.createElement("span",{className:`${N}-close-icon`,onClick:ue},ae);return(0,A.wm)(ae,ze,_=>({onClick:me=>{var ge;(ge=_==null?void 0:_.onClick)===null||ge===void 0||ge.call(_,me),ue(me)},className:O()(_==null?void 0:_.className,`${N}-close-icon`)}))}}),Te=typeof z.onClick=="function"||F&&F.type==="a",xe=R||null,de=xe?a.createElement(a.Fragment,null,xe,F&&a.createElement("span",null,F)):F,Re=a.createElement("span",Object.assign({},je,{ref:g,className:Ve,style:Ze}),de,Ge,Me&&a.createElement(H,{key:"preset",prefixCls:N}),ce&&a.createElement(se,{key:"status",prefixCls:N}));return Ke(Te?a.createElement(p.Z,{component:"Tag"},Re):Re)});le.CheckableTag=E;var Z=le}}]);
