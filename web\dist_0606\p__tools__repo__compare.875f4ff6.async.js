"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9064],{85425:function(G,v,e){var u=e(5574),g=e.n(u),W=e(20057),y=e(85673),b=e(84226),a=e(31622),m=e(67294),P=e(85893),L=function(l){var M=l.proName,x=l.currentPath,j=l.customItems,n=(0,W.TH)(),s=(0,W.s0)(),B=(0,b.useIntl)(),D=(0,m.useState)((0,a.gh)()||"dark"),o=g()(D,2),t=o[0],p=o[1];(0,m.useEffect)(function(){p((0,a.gh)());var E=new MutationObserver(function(d){d.forEach(function(f){f.attributeName==="data-theme"&&p((0,a.gh)())})});return E.observe(document.body,{attributes:!0}),function(){E.disconnect()}},[]);var R=function(){if(j)return j;var d=n.pathname,f=[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801\u4ED3\u5E93"}];if(d.startsWith("/tools/repo/newProject"))return[].concat(f,[{path:"/tools/repo/newProject",breadcrumbName:"\u65B0\u5EFA\u9879\u76EE"}]);if(d.startsWith("/tools/repo/files/newFile"))return[].concat(f,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"},{path:"/tools/repo/files/newFile",breadcrumbName:"\u65B0\u5EFA\u6587\u4EF6"}]);if(d.startsWith("/tools/repo/files")){var A=[].concat(f,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"}]);if(M&&A.push({path:"/tools/repo/files",breadcrumbName:M}),x){var w=x.split("/").filter(Boolean),_="";w.forEach(function(Z,q){_+="/".concat(Z),A.push({path:"/tools/repo/files?path=".concat(_),breadcrumbName:Z})})}return A}else{if(d.startsWith("/tools/repo/branch"))return[].concat(f,[{path:"/tools/repo/branch",breadcrumbName:"\u5206\u652F\u7BA1\u7406"}]);if(d.startsWith("/tools/repo/commits"))return[].concat(f,[{path:"/tools/repo/commits",breadcrumbName:"\u63D0\u4EA4\u7BA1\u7406"}]);if(d.startsWith("/tools/repo/compare"))return[].concat(f,[{path:"/tools/repo/compare",breadcrumbName:"\u6BD4\u8F83\u4FEE\u8BA2\u7248\u672C"}]);if(d.startsWith("/tools/repo/newTag"))return[].concat(f,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"},{path:"/tools/repo/newTag",breadcrumbName:"\u65B0\u5EFA\u6807\u7B7E"}]);if(d.startsWith("/tools/repo/tags"))return[].concat(f,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"}])}return f};return(0,P.jsx)(y.Z,{separator:"/",style:{fontSize:"14px",marginBottom:"16px",color:t==="light"?"#666":"#aaa"},items:R(),itemRender:function(d,f,A){var w=A.indexOf(d)===A.length-1;return w?(0,P.jsx)("span",{style:{color:t==="light"?"#333":"#fff"},children:d.breadcrumbName}):(0,P.jsx)("a",{onClick:function(){return d.path?s(d.path):null},style:{color:t==="light"?"#666":"#aaa"},children:d.breadcrumbName})}})};v.Z=L},94576:function(G,v,e){e.r(v);var u=e(15009),g=e.n(u),W=e(99289),y=e.n(W),b=e(5574),a=e.n(b),m=e(84017),P=e(71471),L=e(34041),K=e(2453),l=e(71230),M=e(15746),x=e(78957),j=e(83622),n=e(67294),s=e(20057),B=e(7528),D=e(85425),o=e(44394),t=e(85893),p=P.Z.Title,R=P.Z.Paragraph,E=L.Z.Option;v.default=function(){var d=(0,s.TH)(),f=d.state||{},A=f.proId,w=f.swapSource,_=f.swapTarget,Z=(0,n.useState)((0,o.gz)()),q=a()(Z,1),V=q[0],$=(0,n.useState)([]),X=a()($,2),N=X[0],F=X[1],O=(0,n.useState)(""),h=a()(O,2),T=h[0],Y=h[1],ae=(0,n.useState)(""),ee=a()(ae,2),k=ee[0],se=ee[1],Oe=(0,n.useState)(!1),le=a()(Oe,2),ce=le[0],ie=le[1],be=(0,n.useState)([]),_e=a()(be,2),De=_e[0],Ce=_e[1],ye=(0,n.useState)([]),de=a()(ye,2),Me=de[0],Be=de[1],Te=(0,n.useState)(""),fe=a()(Te,2),H=fe[0],te=fe[1],Se=(0,n.useState)(""),he=a()(Se,2),J=he[0],re=he[1],Ie=(0,n.useState)(!1),me=a()(Ie,2),Re=me[0],pe=me[1],we=(0,n.useState)(!1),ve=a()(we,2),xe=ve[0],ge=ve[1],Ae=(0,n.useState)(A),Ee=a()(Ae,2),z=Ee[0],oe=Ee[1],We=(0,n.useState)(A),Pe=a()(We,2),Q=Pe[0],ue=Pe[1];(0,n.useEffect)(function(){w&&_&&(Y(w.project),oe(w.projectId),te(w.branch),se(_.project),ue(_.projectId),re(_.branch))},[w,_]),(0,n.useEffect)(function(){var c=function(){var I=y()(g()().mark(function C(){var r,U,ne;return g()().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return ie(!0),S.prev=1,S.next=4,(0,B.hW)();case 4:r=S.sent,Array.isArray(r)&&r.length>0&&(F(r),!T&&r.length>0&&(U=r[0],Y(U.path_with_namespace||""),oe(U.id)),!k&&r.length>0&&(ne=r[0],se(ne.path_with_namespace||""),ue(ne.id))),S.next=12;break;case 8:S.prev=8,S.t0=S.catch(1),console.error("\u83B7\u53D6\u9879\u76EE\u5217\u8868\u5931\u8D25:",S.t0),K.ZP.error("\u83B7\u53D6\u9879\u76EE\u5217\u8868\u5931\u8D25");case 12:return S.prev=12,ie(!1),S.finish(12);case 15:case"end":return S.stop()}},C,null,[[1,8,12,15]])}));return function(){return I.apply(this,arguments)}}();c()},[]),(0,n.useEffect)(function(){if(z){var c=function(){var I=y()(g()().mark(function C(){var r,U;return g()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return pe(!0),i.prev=1,i.next=4,(0,B.$y)({id:z});case 4:r=i.sent,Array.isArray(r)&&r.length>0&&(Ce(r),!H&&r.length>0&&(U=r.find(function(S){return S.default===!0}),te(U?U.name:r[0].name))),i.next=12;break;case 8:i.prev=8,i.t0=i.catch(1),console.error("\u83B7\u53D6\u6E90\u9879\u76EE\u5206\u652F\u5217\u8868\u5931\u8D25:",i.t0),K.ZP.error("\u83B7\u53D6\u6E90\u9879\u76EE\u5206\u652F\u5217\u8868\u5931\u8D25");case 12:return i.prev=12,pe(!1),i.finish(12);case 15:case"end":return i.stop()}},C,null,[[1,8,12,15]])}));return function(){return I.apply(this,arguments)}}();c()}},[z]),(0,n.useEffect)(function(){if(Q){var c=function(){var I=y()(g()().mark(function C(){var r,U;return g()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return ge(!0),i.prev=1,i.next=4,(0,B.$y)({id:Q});case 4:r=i.sent,Array.isArray(r)&&r.length>0&&(Be(r),!J&&r.length>0&&(U=r.find(function(S){return S.default===!0}),re(U?U.name:r[0].name))),i.next=12;break;case 8:i.prev=8,i.t0=i.catch(1),console.error("\u83B7\u53D6\u76EE\u6807\u9879\u76EE\u5206\u652F\u5217\u8868\u5931\u8D25:",i.t0),K.ZP.error("\u83B7\u53D6\u76EE\u6807\u9879\u76EE\u5206\u652F\u5217\u8868\u5931\u8D25");case 12:return i.prev=12,ge(!1),i.finish(12);case 15:case"end":return i.stop()}},C,null,[[1,8,12,15]])}));return function(){return I.apply(this,arguments)}}();c()}},[Q]);var Le=function(I){Y(I);var C=N.find(function(r){return r.path_with_namespace===I});C&&oe(C.id)},$e=function(I){se(I);var C=N.find(function(r){return r.path_with_namespace===I});C&&ue(C.id)},Ue=function(){var I=T;Y(k),se(I);var C=z;oe(Q),ue(C);var r=H;te(J),re(r)},je=(0,s.s0)(),Ke=function(){if(!z||!Q||!H||!J){K.ZP.error("\u8BF7\u9009\u62E9\u8981\u6BD4\u8F83\u7684\u9879\u76EE\u548C\u5206\u652F");return}console.log("Comparing",T,H,"with",k,J);var I=N.find(function(r){return r.id===z}),C=N.find(function(r){return r.id===Q});je("/tools/repo/compare/compare_result",{state:{sourceProjectId:z,targetProjectId:Q,sourceProject:T,targetProject:k,sourceBranch:H,targetBranch:J,sourceProjectInfo:I,targetProjectInfo:C}})},Ze=function(){if(!z||!Q||!H||!J){K.ZP.error("\u8BF7\u9009\u62E9\u8981\u5408\u5E76\u7684\u9879\u76EE\u548C\u5206\u652F");return}console.log("Creating merge request from",T,H,"to",k,J),je("/tools/repo/newMergeRequest",{state:{proId:z,proName:T,sourceBranch:H,targetBranch:J}})};return(0,t.jsx)(m._z,{header:{title:"",breadcrumb:(0,t.jsx)(D.Z,{customItems:[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801"},{path:"/tools/repo/compare",breadcrumbName:"\u6BD4\u8F83\u4FEE\u8BA2\u7248\u672C"}]})},children:(0,t.jsxs)("div",{children:[(0,t.jsx)(p,{level:2,style:{marginBottom:24,fontWeight:"normal"},children:"\u6BD4\u8F83\u7248\u672C"}),(0,t.jsxs)(R,{style:{fontSize:16,marginBottom:32},children:["\u66F4\u6539\u663E\u793A\u4E3A\u6E90\u7248\u672C\u6B63\u5728\u5408\u5E76\u5230\u76EE\u6807\u7248\u672C\u3002",(0,t.jsx)("a",{children:"\u4E86\u89E3\u66F4\u591A\u5173\u4E8E\u6BD4\u8F83\u7248\u672C\u7684\u4FE1\u606F\u3002"})]}),(0,t.jsxs)(l.Z,{gutter:[24,24],align:"middle",children:[(0,t.jsxs)(M.Z,{span:11,children:[(0,t.jsx)(p,{level:4,style:{marginBottom:12,fontWeight:"normal"},children:"\u6765\u6E90"}),(0,t.jsxs)(l.Z,{gutter:[8,0],children:[(0,t.jsx)(M.Z,{span:12,children:(0,t.jsx)(L.Z,{style:{width:"100%"},value:T,onChange:Le,dropdownStyle:(0,o.XE)(V),loading:ce,suffixIcon:(0,t.jsx)("span",{style:{color:"#666"},children:"\u25BC"}),placeholder:"\u9009\u62E9\u9879\u76EE",children:N.map(function(c){return(0,t.jsx)(E,{value:c.path_with_namespace||"",children:c.path_with_namespace||c.name},c.id)})})}),(0,t.jsx)(M.Z,{span:12,children:(0,t.jsx)(L.Z,{style:{width:"100%"},value:H,onChange:te,dropdownStyle:(0,o.XE)(V),loading:Re,suffixIcon:(0,t.jsx)("span",{style:{color:"#666"},children:"\u25BC"}),placeholder:"\u9009\u62E9\u5206\u652F",disabled:!z,children:De.map(function(c){return(0,t.jsx)(E,{value:c.name,children:c.name},c.name)})})})]})]}),(0,t.jsx)(M.Z,{span:2,style:{display:"flex",justifyContent:"center",alignItems:"flex-end",height:"32px"},children:(0,t.jsx)("div",{style:{fontSize:16,color:"#999",marginBottom:"5px"},children:"-"})}),(0,t.jsxs)(M.Z,{span:11,children:[(0,t.jsx)(p,{level:4,style:{marginBottom:12,fontWeight:"normal"},children:"\u76EE\u6807"}),(0,t.jsxs)(l.Z,{gutter:[8,0],children:[(0,t.jsx)(M.Z,{span:12,children:(0,t.jsx)(L.Z,{style:{width:"100%"},value:k,onChange:$e,dropdownStyle:(0,o.XE)(V),loading:ce,suffixIcon:(0,t.jsx)("span",{style:{color:"#666"},children:"\u25BC"}),placeholder:"\u9009\u62E9\u9879\u76EE",children:N.map(function(c){return(0,t.jsx)(E,{value:c.path_with_namespace||"",children:c.path_with_namespace||c.name},c.id)})})}),(0,t.jsx)(M.Z,{span:12,children:(0,t.jsx)(L.Z,{style:{width:"100%"},value:J,onChange:re,dropdownStyle:(0,o.XE)(V),loading:xe,suffixIcon:(0,t.jsx)("span",{style:{color:"#666"},children:"\u25BC"}),placeholder:"\u9009\u62E9\u5206\u652F",disabled:!Q,children:Me.map(function(c){return(0,t.jsx)(E,{value:c.name,children:c.name},c.name)})})})]})]})]}),(0,t.jsx)(l.Z,{style:{marginTop:24},children:(0,t.jsx)(M.Z,{children:(0,t.jsxs)(x.Z,{size:8,children:[(0,t.jsx)(j.ZP,{type:"primary",onClick:Ke,style:{backgroundColor:"#1677ff",height:"32px"},children:"\u6BD4\u8F83"}),(0,t.jsx)(j.ZP,{onClick:Ue,style:{borderColor:"#d9d9d9",height:"32px"},children:"\u4EA4\u6362\u7248\u672C"})]})})})]})})}},44438:function(G,v,e){e.d(v,{E_:function(){return W},ct:function(){return u}});var u=function(b){if(!b)return"";var a=new Date,m=new Date(b),P=Math.abs(a.getTime()-m.getTime())/(1e3*60*60);return P>=24?"".concat(Math.round(P/24),"\u5929\u524D"):"".concat(Math.round(P),"\u5C0F\u65F6\u524D")},g=function(b){if(!b)return!1;var a=new Date,m=new Date(b),P=(a.getTime()-m.getTime())/(1e3*60*60*24);return P<=5},W=function(b){if(!b)return!1;var a=new Date,m=new Date(b),P=Math.abs(a.getTime()-m.getTime())/(1e3*60*60);return P<720}},44394:function(G,v,e){e.d(v,{E_:function(){return u.E_},XE:function(){return g.XE},gz:function(){return g.gz}});var u=e(44438),g=e(46671)},46671:function(G,v,e){e.d(v,{XE:function(){return b},gz:function(){return g}});var u=e(31622),g=function(){return(0,u.gh)()||"dark"},W=function(m){return m==="light"?"#fff":"#1a1c1e"},y=function(m){return m==="light"?"#1a1c1e":"#fff"},b=function(m){return{background:W(m),color:y(m)}}},15746:function(G,v,e){var u=e(21584);v.Z=u.Z},99134:function(G,v,e){var u=e(67294);const g=(0,u.createContext)({});v.Z=g},21584:function(G,v,e){var u=e(67294),g=e(93967),W=e.n(g),y=e(53124),b=e(99134),a=e(6999),m=function(l,M){var x={};for(var j in l)Object.prototype.hasOwnProperty.call(l,j)&&M.indexOf(j)<0&&(x[j]=l[j]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,j=Object.getOwnPropertySymbols(l);n<j.length;n++)M.indexOf(j[n])<0&&Object.prototype.propertyIsEnumerable.call(l,j[n])&&(x[j[n]]=l[j[n]]);return x};function P(l){return typeof l=="number"?`${l} ${l} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(l)?`0 0 ${l}`:l}const L=["xs","sm","md","lg","xl","xxl"],K=u.forwardRef((l,M)=>{const{getPrefixCls:x,direction:j}=u.useContext(y.E_),{gutter:n,wrap:s}=u.useContext(b.Z),{prefixCls:B,span:D,order:o,offset:t,push:p,pull:R,className:E,children:d,flex:f,style:A}=l,w=m(l,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),_=x("col",B),[Z,q,V]=(0,a.cG)(_),$={};let X={};L.forEach(O=>{let h={};const T=l[O];typeof T=="number"?h.span=T:typeof T=="object"&&(h=T||{}),delete w[O],X=Object.assign(Object.assign({},X),{[`${_}-${O}-${h.span}`]:h.span!==void 0,[`${_}-${O}-order-${h.order}`]:h.order||h.order===0,[`${_}-${O}-offset-${h.offset}`]:h.offset||h.offset===0,[`${_}-${O}-push-${h.push}`]:h.push||h.push===0,[`${_}-${O}-pull-${h.pull}`]:h.pull||h.pull===0,[`${_}-rtl`]:j==="rtl"}),h.flex&&(X[`${_}-${O}-flex`]=!0,$[`--${_}-${O}-flex`]=P(h.flex))});const N=W()(_,{[`${_}-${D}`]:D!==void 0,[`${_}-order-${o}`]:o,[`${_}-offset-${t}`]:t,[`${_}-push-${p}`]:p,[`${_}-pull-${R}`]:R},E,X,q,V),F={};if(n&&n[0]>0){const O=n[0]/2;F.paddingLeft=O,F.paddingRight=O}return f&&(F.flex=P(f),s===!1&&!F.minWidth&&(F.minWidth=0)),Z(u.createElement("div",Object.assign({},w,{style:Object.assign(Object.assign(Object.assign({},F),A),$),className:N,ref:M}),d))});v.Z=K},17621:function(G,v,e){e.d(v,{Z:function(){return n}});var u=e(67294),g=e(93967),W=e.n(g),y=e(74443),b=e(53124),a=e(25378);function m(s,B){const D=[void 0,void 0],o=Array.isArray(s)?s:[s,void 0],t=B||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return o.forEach((p,R)=>{if(typeof p=="object"&&p!==null)for(let E=0;E<y.c4.length;E++){const d=y.c4[E];if(t[d]&&p[d]!==void 0){D[R]=p[d];break}}else D[R]=p}),D}var P=e(99134),L=e(6999),K=function(s,B){var D={};for(var o in s)Object.prototype.hasOwnProperty.call(s,o)&&B.indexOf(o)<0&&(D[o]=s[o]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var t=0,o=Object.getOwnPropertySymbols(s);t<o.length;t++)B.indexOf(o[t])<0&&Object.prototype.propertyIsEnumerable.call(s,o[t])&&(D[o[t]]=s[o[t]]);return D};const l=null,M=null;function x(s,B){const[D,o]=u.useState(typeof s=="string"?s:""),t=()=>{if(typeof s=="string"&&o(s),typeof s=="object")for(let p=0;p<y.c4.length;p++){const R=y.c4[p];if(!B||!B[R])continue;const E=s[R];if(E!==void 0){o(E);return}}};return u.useEffect(()=>{t()},[JSON.stringify(s),B]),D}var n=u.forwardRef((s,B)=>{const{prefixCls:D,justify:o,align:t,className:p,style:R,children:E,gutter:d=0,wrap:f}=s,A=K(s,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:w,direction:_}=u.useContext(b.E_),Z=(0,a.Z)(!0,null),q=x(t,Z),V=x(o,Z),$=w("row",D),[X,N,F]=(0,L.VM)($),O=m(d,Z),h=W()($,{[`${$}-no-wrap`]:f===!1,[`${$}-${V}`]:V,[`${$}-${q}`]:q,[`${$}-rtl`]:_==="rtl"},p,N,F),T={},Y=O[0]!=null&&O[0]>0?O[0]/-2:void 0;Y&&(T.marginLeft=Y,T.marginRight=Y);const[ae,ee]=O;T.rowGap=ee;const k=u.useMemo(()=>({gutter:[ae,ee],wrap:f}),[ae,ee,f]);return X(u.createElement(P.Z.Provider,{value:k},u.createElement("div",Object.assign({},A,{className:h,style:Object.assign(Object.assign({},T),R),ref:B}),E)))})},71230:function(G,v,e){var u=e(17621);v.Z=u.Z}}]);
