"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2824],{92371:function(Z,p,e){e.r(p);var O=e(52677),T=e.n(O),W=e(15009),v=e.n(W),i=e(19632),d=e.n(i),u=e(99289),E=e.n(u),Ee=e(5574),I=e.n(Ee),c=e(67294),me=e(84017),X=e(11941),ve=e(71471),$=e(2453),ge=e(74330),F=e(2487),De=e(85357),G=e(78957),pe=e(66309),J=e(20057),Q=e(57184),V=e(18429),Y=e(8751),he=e(82099),Pe=e(48118),je=e(71255),be=e(15360),Me=e(48474),Oe=e(55355),Te=e(40666),Ie=e(44438),N=e(44394),q=e(7528),t=e(85893),h=X.Z.TabPane,K=ve.Z.Text,Ce=function(f,P){switch(f){case"push":return(0,t.jsx)(Q.Z,{});case"merge":return P==="error"||P==="warning"?(0,t.jsx)(V.Z,{}):P==="success"?(0,t.jsx)(Y.Z,{}):(0,t.jsx)(he.Z,{});case"issue":return P==="error"?(0,t.jsx)(V.Z,{}):P==="success"?(0,t.jsx)(Y.Z,{}):(0,t.jsx)(Pe.Z,{});case"comment":return(0,t.jsx)(je.Z,{});case"wiki":return(0,t.jsx)(be.Z,{});case"design":return(0,t.jsx)(Me.Z,{});case"team":return(0,t.jsx)(Oe.Z,{});case"tag":return(0,t.jsx)(Te.Z,{});default:return(0,t.jsx)(Q.Z,{})}},Ae=function(f){return f.includes("Closed")||f.includes("Deleted")?"#f5222d":f.includes("Opened")||f.includes("Pushed")?"#52c41a":f.includes("Commented")?"#1890ff":"inherit"},Be=function(f){switch(f){case"success":return"#52c41a";case"warning":return"#faad14";case"error":return"#f5222d";default:return"#1890ff"}},ye=function(){var f=(0,c.useState)("all"),P=I()(f,2),g=P[0],Ue=P[1],Re=(0,c.useState)([]),ee=I()(Re,2),te=ee[0],re=ee[1],We=(0,c.useState)([]),ae=I()(We,2),Le=ae[0],Ke=ae[1],Se=(0,c.useState)(!1),se=I()(Se,2),xe=se[0],ne=se[1],U=(0,J.TH)(),_e=(0,J.s0)(),we=(0,c.useState)(""),ue=I()(we,2),oe=ue[0],ie=ue[1],Ze=(0,c.useState)(0),le=I()(Ze,2),R=le[0],k=le[1],Fe=(0,c.useState)((0,N.gz)()),de=I()(Fe,2),Ne=de[0],ce=de[1];(0,c.useEffect)(function(){ce((0,N.gz)());var j=new MutationObserver(function(a){a.forEach(function(b){b.attributeName==="data-theme"&&ce((0,N.gz)())})});return j.observe(document.body,{attributes:!0}),function(){j.disconnect()}},[]),(0,c.useEffect)(function(){var j=function(){var a=E()(v()().mark(function b(){var o,s,m,C,A,B,M;return v()().wrap(function(_){for(;;)switch(_.prev=_.next){case 0:if(o=localStorage.getItem("currentProjectId"),!(U.state&&U.state.proId)){_.next=8;break}s=U.state.proId,k(s),U.state.proName&&ie(U.state.proName),localStorage.setItem("currentProjectId",String(s)),_.next=24;break;case 8:if(!o){_.next=13;break}m=Number(o),k(m),_.next=24;break;case 13:return _.prev=13,_.next=16,(0,q.hW)();case 16:C=_.sent,C&&C.length>0?(A=d()(C).sort(function(r,n){var L=new Date(r.last_activity_at||0),D=new Date(n.last_activity_at||0);return D.getTime()-L.getTime()}),B=A[0],M=B.id,k(M),ie(B.name||""),localStorage.setItem("currentProjectId",String(M))):console.warn("\u672A\u627E\u5230\u4EFB\u4F55\u9879\u76EE"),_.next=24;break;case 20:_.prev=20,_.t0=_.catch(13),console.error("Failed to fetch project information:",_.t0),$.ZP.error("\u83B7\u53D6\u9879\u76EE\u4FE1\u606F\u5931\u8D25");case 24:case"end":return _.stop()}},b,null,[[13,20]])}));return function(){return a.apply(this,arguments)}}();j()},[U]),(0,c.useEffect)(function(){var j=function(){var a=E()(v()().mark(function b(){var o,s,m,C,A,B,M,S;return v()().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(R){r.next=2;break}return r.abrupt("return");case 2:return ne(!0),r.prev=3,o={proId:R,userId:2},g==="push"?o.action="pushed":g==="merge"?o.action="merged":g==="comment"?o.action="commented":g==="issue"?o.targetType="issue":(g==="wiki"||g==="design"||g==="team")&&(o.targetType="note"),console.log("\u83B7\u53D6\u4E8B\u4EF6\uFF0C\u53C2\u6570:",o),r.next=9,(0,q.yc)(o);case 9:if(s=r.sent,!s){r.next=37;break}if(m=[],!Array.isArray(s)){r.next=16;break}m=s,r.next=35;break;case 16:if(!(s&&T()(s)==="object")){r.next=34;break}if(!(s.data&&Array.isArray(s.data))){r.next=21;break}m=s.data,r.next=32;break;case 21:C=["events","items","list","results"],A=0,B=C;case 23:if(!(A<B.length)){r.next=31;break}if(M=B[A],!(s[M]&&Array.isArray(s[M]))){r.next=28;break}return m=s[M],r.abrupt("break",31);case 28:A++,r.next=23;break;case 31:m.length===0&&(m=[s]);case 32:r.next=35;break;case 34:console.warn("\u65E0\u6CD5\u89E3\u6790\u7684\u54CD\u5E94\u6570\u636E\u683C\u5F0F:",s);case 35:S=m.map(function(n){var L,D="push",x="default",y="",l=String(n.action_name||"");l.includes("push")?(D="push",y=l.includes("delete")?"Deleted branch":"Pushed new branch",x=l.includes("delete")?"warning":"success"):l.includes("merge")?(D="merge",y=l.includes("close")?"Closed merge request":"Opened merge request",x=l.includes("close")?"error":"success"):l.includes("issue")?(D="issue",y=l.includes("close")?"Closed issue":"Opened issue",x=l.includes("close")?"error":"success"):l.includes("comment")?(D="comment",y="Commented on merge request"):l.includes("tag")?(D="tag",y="Pushed new tag"):(D="push",y=l||"Unknown action");var H="Unknown";n.author&&n.author.name?H=n.author.name:n.author_username&&(H=n.author_username);var w="";n.target_title?w=String(n.target_title):n.push_data?w=String(n.push_data.ref):w="Unknown target";var fe=new Date().toISOString();if(n.created_at)try{fe=new Date(n.created_at).toISOString()}catch(He){console.error("\u65E0\u6CD5\u89E3\u6790\u65F6\u95F4\u6233:",n.created_at,He)}var ke={id:String(n.id||""),type:D,username:H,userAvatar:(L=n.author)===null||L===void 0?void 0:L.avatar_url,action:y,target:w,timestamp:fe,status:x};return ke}),S.length>0?re(S):(console.warn("\u683C\u5F0F\u5316\u540E\u6CA1\u6709\u6709\u6548\u6570\u636E"),re([]));case 37:r.next=43;break;case 39:r.prev=39,r.t0=r.catch(3),console.error("\u83B7\u53D6\u4E8B\u4EF6\u6570\u636E\u5931\u8D25:",r.t0),$.ZP.error("\u83B7\u53D6\u4E8B\u4EF6\u6570\u636E\u5931\u8D25");case 43:return r.prev=43,ne(!1),r.finish(43);case 46:case"end":return r.stop()}},b,null,[[3,39,43,46]])}));return function(){return a.apply(this,arguments)}}();R&&j()},[R,g]),(0,c.useEffect)(function(){Ke(te)},[te]);var ze=function(a){a.type==="merge"?_e("/tools/repo/mergeRequests",{state:{proId:R,proName:oe}}):(a.type==="push"||a.type==="tag")&&_e("/tools/repo/branch",{state:{proId:R,proName:oe}})};return(0,t.jsx)(me._z,{header:{title:""},children:(0,t.jsxs)("div",{style:{padding:"0",borderRadius:"4px"},children:[(0,t.jsxs)(X.Z,{activeKey:g,onChange:Ue,children:[(0,t.jsx)(h,{tab:"\u5168\u90E8"},"all"),(0,t.jsx)(h,{tab:"\u63A8\u9001\u4E8B\u4EF6"},"push"),(0,t.jsx)(h,{tab:"\u5408\u5E76\u4E8B\u4EF6"},"merge"),(0,t.jsx)(h,{tab:"\u8BAE\u9898\u4E8B\u4EF6"},"issue"),(0,t.jsx)(h,{tab:"\u8BC4\u8BBA"},"comment"),(0,t.jsx)(h,{tab:"Wiki"},"wiki"),(0,t.jsx)(h,{tab:"\u8BBE\u8BA1"},"design"),(0,t.jsx)(h,{tab:"\u56E2\u961F"},"team")]}),(0,t.jsx)(ge.Z,{spinning:xe,children:(0,t.jsx)(F.Z,{itemLayout:"horizontal",dataSource:Le,locale:{emptyText:"\u6682\u65E0\u6570\u636E"},renderItem:function(a){return(0,t.jsxs)(F.Z.Item,{style:{cursor:"pointer",padding:"16px 24px",borderBottom:"1px solid ".concat(Ne==="light"?"#f0f0f0":"#303030")},onClick:function(){return ze(a)},children:[(0,t.jsx)(F.Z.Item.Meta,{avatar:(0,t.jsx)(De.Z,{style:{backgroundColor:Be(a.status)},icon:Ce(a.type,a.status)}),title:(0,t.jsxs)(G.Z,{children:[(0,t.jsx)(K,{strong:!0,children:a.username}),(0,t.jsxs)(K,{type:"secondary",children:["@",a.username.toLowerCase()]})]}),description:(0,t.jsxs)("div",{children:[(0,t.jsxs)(G.Z,{children:[(0,t.jsx)(K,{style:{color:Ae(a.action)},children:a.action}),(0,t.jsx)(K,{strong:!0,children:a.target})]}),a.branch&&(0,t.jsx)("div",{style:{marginTop:4},children:(0,t.jsx)(pe.Z,{color:"blue",children:a.branch})})]})}),(0,t.jsx)("div",{children:(0,Ie.ct)(a.timestamp)})]})}})})]})})};p.default=ye},44438:function(Z,p,e){e.d(p,{E_:function(){return W},ct:function(){return O}});var O=function(i){if(!i)return"";var d=new Date,u=new Date(i),E=Math.abs(d.getTime()-u.getTime())/(1e3*60*60);return E>=24?"".concat(Math.round(E/24),"\u5929\u524D"):"".concat(Math.round(E),"\u5C0F\u65F6\u524D")},T=function(i){if(!i)return!1;var d=new Date,u=new Date(i),E=(d.getTime()-u.getTime())/(1e3*60*60*24);return E<=5},W=function(i){if(!i)return!1;var d=new Date,u=new Date(i),E=Math.abs(d.getTime()-u.getTime())/(1e3*60*60);return E<720}},44394:function(Z,p,e){e.d(p,{E_:function(){return O.E_},XE:function(){return T.XE},gz:function(){return T.gz}});var O=e(44438),T=e(46671)},46671:function(Z,p,e){e.d(p,{XE:function(){return i},gz:function(){return T}});var O=e(31622),T=function(){return(0,O.gh)()||"dark"},W=function(u){return u==="light"?"#fff":"#1a1c1e"},v=function(u){return u==="light"?"#1a1c1e":"#fff"},i=function(u){return{background:W(u),color:v(u)}}}}]);
