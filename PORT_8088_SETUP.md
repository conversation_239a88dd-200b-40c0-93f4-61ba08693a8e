# 8088端口专用配置指南

## 概述

本指南专门针对只开放8088端口的服务器环境，提供完整的nginx容器化部署方案。

## 端口配置说明

### 当前配置
- **宿主机开放端口**: 8088
- **容器内nginx监听**: 8088
- **端口映射**: 8088:8088
- **后端API端口**: 9021（容器内通过host.docker.internal访问）
- **WebSocket端口**: 8099（容器内通过host.docker.internal访问）

### 配置文件修改

#### nginx-container.conf
```nginx
server {
    listen 8088;  # 容器内监听8088端口
    server_name ************* localhost;
    # ... 其他配置
}
```

#### Docker运行命令
```bash
docker run -d \
  --name xinhe-nginx-8088 \
  -p 8088:8088 \  # 宿主机8088映射到容器8088
  # ... 其他配置
```

## 快速部署

### 一键部署脚本

```bash
# 使用专用的8088端口部署脚本
chmod +x deploy-8088.sh
./deploy-8088.sh
```

### 手动部署步骤

```bash
# 1. 构建前端
cd web
npm run build
cd ..

# 2. 注入HTTPS环境标识
node inject-https-meta.js

# 3. 停止现有容器
docker stop xinhe-nginx-8088 2>/dev/null || true
docker rm xinhe-nginx-8088 2>/dev/null || true

# 4. 启动新容器
docker run -d \
  --name xinhe-nginx-8088 \
  -p 8088:8088 \
  -v $(pwd)/nginx-container.conf:/etc/nginx/nginx.conf:ro \
  -v $(pwd)/web/dist:/usr/share/nginx/html:ro \
  --add-host host.docker.internal:host-gateway \
  --restart unless-stopped \
  nginx:alpine

# 5. 验证部署
docker ps | grep xinhe-nginx-8088
curl -I http://*************:8088
```

## 验证配置

### 检查容器状态
```bash
# 查看容器是否运行
docker ps | grep xinhe-nginx-8088

# 查看容器日志
docker logs xinhe-nginx-8088

# 检查nginx配置
docker exec xinhe-nginx-8088 nginx -t
```

### 测试访问
```bash
# 测试HTTP访问
curl -I http://*************:8088

# 检查HTTPS模拟头
curl -I http://*************:8088 | grep -i "x-forwarded"

# 测试API代理
curl -I http://*************:8088/api/health
```

### 预期响应头
```
HTTP/1.1 200 OK
X-Forwarded-Proto: https
X-Forwarded-Port: 443
X-Forwarded-Ssl: on
X-Secure-Context: true
```

## 故障排除

### 常见问题

1. **端口占用错误**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 8088
   
   # 停止占用端口的进程
   docker stop xinhe-nginx-8088
   ```

2. **容器启动失败**
   ```bash
   # 查看详细错误
   docker logs xinhe-nginx-8088
   
   # 检查nginx配置
   docker exec xinhe-nginx-8088 nginx -t
   ```

3. **nginx配置错误**
   ```bash
   # 验证配置文件语法
   nginx -t -c nginx-container.conf
   
   # 检查监听端口配置
   grep "listen" nginx-container.conf
   ```

4. **API代理失败**
   ```bash
   # 检查后端服务是否运行
   netstat -tlnp | grep 9021
   
   # 测试容器内网络连接
   docker exec xinhe-nginx-8088 ping host.docker.internal
   ```

### 调试命令

```bash
# 进入容器调试
docker exec -it xinhe-nginx-8088 sh

# 在容器内测试nginx配置
nginx -t

# 在容器内查看进程
ps aux | grep nginx

# 在容器内测试网络连接
wget -O- http://host.docker.internal:9021/api/health
```

## 网络架构

```
外部访问 → 宿主机:8088 → 容器:8088 → nginx
                                    ↓
                              静态文件服务
                                    ↓
                         API代理 → host.docker.internal:9021
                                    ↓
                      WebSocket代理 → host.docker.internal:8099
```

## 安全考虑

### 防火墙配置
```bash
# 只开放8088端口
ufw allow 8088/tcp
ufw deny 80/tcp
ufw deny 443/tcp
```

### 容器安全
```bash
# 使用非特权用户运行
docker run --user nginx:nginx ...

# 限制容器资源
docker run --memory=512m --cpus=1 ...

# 只读挂载配置文件
-v $(pwd)/nginx-container.conf:/etc/nginx/nginx.conf:ro
```

## 性能优化

### nginx优化
```nginx
# 工作进程数
worker_processes auto;

# 连接数限制
worker_connections 1024;

# 启用gzip压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

### 容器优化
```bash
# 使用alpine镜像减少体积
nginx:alpine

# 设置重启策略
--restart unless-stopped

# 优化内存使用
--memory=512m
```

## 监控和日志

### 日志查看
```bash
# 实时查看nginx日志
docker logs -f xinhe-nginx-8088

# 查看访问日志
docker exec xinhe-nginx-8088 tail -f /var/log/nginx/access.log

# 查看错误日志
docker exec xinhe-nginx-8088 tail -f /var/log/nginx/error.log
```

### 健康检查
```bash
# 添加健康检查
docker run --health-cmd="curl -f http://localhost:8088/health || exit 1" \
           --health-interval=30s \
           --health-timeout=10s \
           --health-retries=3 \
           ...
```

## 更新和维护

### 更新应用
```bash
# 重新构建和部署
./deploy-8088.sh

# 或者手动更新
cd web && npm run build && cd ..
node inject-https-meta.js
docker restart xinhe-nginx-8088
```

### 备份配置
```bash
# 备份nginx配置
cp nginx-container.conf nginx-container.conf.backup

# 备份容器配置
docker inspect xinhe-nginx-8088 > container-config.json
```

## 总结

8088端口专用配置的关键点：

1. ✅ **nginx监听8088端口**
2. ✅ **Docker端口映射8088:8088**
3. ✅ **HTTPS环境模拟正常工作**
4. ✅ **Web Crypto API可用**
5. ✅ **API和WebSocket代理正常**

这个配置完美适配只开放8088端口的服务器环境！
