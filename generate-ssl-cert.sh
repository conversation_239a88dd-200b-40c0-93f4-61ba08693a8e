#!/bin/bash

# SSL证书生成脚本
# 用于生成自签名证书（开发/测试环境）或配置Let's Encrypt证书（生产环境）

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== SSL证书配置脚本 ===${NC}"

# 配置变量
SERVER_IP="*************"
DOMAIN_NAME="$SERVER_IP"  # 如果有域名，请替换这里
SSL_DIR="/usr/local/nginx/ssl"
CERT_FILE="$SSL_DIR/cert.pem"
KEY_FILE="$SSL_DIR/key.pem"

# 创建SSL目录
echo -e "${YELLOW}创建SSL证书目录...${NC}"
sudo mkdir -p $SSL_DIR

echo "请选择证书类型："
echo "1) 自签名证书（开发/测试环境）"
echo "2) Let's Encrypt证书（生产环境，需要域名）"
read -p "请输入选择 (1 或 2): " cert_choice

case $cert_choice in
    1)
        echo -e "${YELLOW}生成自签名证书...${NC}"
        
        # 生成私钥
        sudo openssl genrsa -out $KEY_FILE 2048
        
        # 生成证书签名请求配置
        cat > /tmp/cert.conf <<EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=CN
ST=Beijing
L=Beijing
O=XinHe
OU=IT Department
CN=$DOMAIN_NAME

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN_NAME
IP.1 = $SERVER_IP
EOF

        # 生成自签名证书
        sudo openssl req -new -x509 -key $KEY_FILE -out $CERT_FILE -days 365 -config /tmp/cert.conf -extensions v3_req
        
        # 清理临时文件
        rm /tmp/cert.conf
        
        echo -e "${GREEN}自签名证书生成完成！${NC}"
        echo -e "${YELLOW}注意：浏览器会显示安全警告，请点击'高级'然后'继续访问'${NC}"
        ;;
        
    2)
        echo -e "${YELLOW}配置Let's Encrypt证书...${NC}"
        
        # 检查是否安装了certbot
        if ! command -v certbot &> /dev/null; then
            echo -e "${YELLOW}安装certbot...${NC}"
            sudo apt update
            sudo apt install -y certbot python3-certbot-nginx
        fi
        
        # 获取证书
        echo -e "${YELLOW}获取Let's Encrypt证书...${NC}"
        echo -e "${RED}注意：确保域名已正确解析到此服务器IP${NC}"
        read -p "请输入您的域名: " user_domain
        read -p "请输入您的邮箱: " user_email
        
        sudo certbot certonly --nginx -d $user_domain --email $user_email --agree-tos --non-interactive
        
        # 创建符号链接
        sudo ln -sf /etc/letsencrypt/live/$user_domain/fullchain.pem $CERT_FILE
        sudo ln -sf /etc/letsencrypt/live/$user_domain/privkey.pem $KEY_FILE
        
        # 设置自动续期
        echo -e "${YELLOW}设置证书自动续期...${NC}"
        (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
        
        echo -e "${GREEN}Let's Encrypt证书配置完成！${NC}"
        ;;
        
    *)
        echo -e "${RED}无效选择，退出${NC}"
        exit 1
        ;;
esac

# 设置证书文件权限
sudo chmod 600 $KEY_FILE
sudo chmod 644 $CERT_FILE
sudo chown root:root $KEY_FILE $CERT_FILE

echo -e "${GREEN}证书文件权限设置完成${NC}"

# 验证证书
echo -e "${YELLOW}验证证书...${NC}"
if sudo openssl x509 -in $CERT_FILE -text -noout > /dev/null 2>&1; then
    echo -e "${GREEN}证书验证成功！${NC}"
    
    # 显示证书信息
    echo -e "${YELLOW}证书信息：${NC}"
    sudo openssl x509 -in $CERT_FILE -subject -dates -noout
else
    echo -e "${RED}证书验证失败！${NC}"
    exit 1
fi

echo -e "${GREEN}=== SSL证书配置完成 ===${NC}"
echo -e "${YELLOW}下一步：${NC}"
echo "1. 将nginx.conf复制到nginx配置目录"
echo "2. 测试nginx配置：sudo nginx -t"
echo "3. 重启nginx：sudo systemctl restart nginx"
echo "4. 重新构建并部署前端应用"
