#!/usr/bin/env node

/**
 * HTML模板注入脚本
 * 在构建后的HTML文件中注入HTTPS环境标识meta标签
 */

const fs = require('fs');
const path = require('path');

// 配置
const BUILD_DIR = './web/dist';
const INDEX_FILE = 'index.html';

// 要注入的meta标签
const META_TAGS = `
    <!-- HTTPS环境标识 - 用于Web Crypto API检测 -->
    <meta name="x-forwarded-proto" content="https">
    <meta name="x-forwarded-port" content="443">
    <meta name="x-forwarded-ssl" content="on">
    <meta name="x-secure-context" content="true">
    <script>
        // 设置全局变量，用于Keycloak检测HTTPS环境
        window.__SECURE_CONTEXT__ = true;
        window.__FORWARDED_PROTO__ = 'https';
        
        // 模拟安全上下文
        if (!window.isSecureContext) {
            Object.defineProperty(window, 'isSecureContext', {
                value: true,
                writable: false,
                configurable: false
            });
        }
        
        console.log('🔒 HTTPS环境模拟已启用');
        console.log('📊 环境信息:', {
            protocol: window.location.protocol,
            isSecureContext: window.isSecureContext,
            forwardedProto: window.__FORWARDED_PROTO__,
            webCryptoAvailable: !!(window.crypto && window.crypto.subtle)
        });
    </script>`;

function injectMetaTags(htmlContent) {
    // 在</head>标签前插入meta标签
    const headCloseTag = '</head>';
    const headIndex = htmlContent.indexOf(headCloseTag);
    
    if (headIndex === -1) {
        console.error('❌ 未找到</head>标签');
        return htmlContent;
    }
    
    const beforeHead = htmlContent.substring(0, headIndex);
    const afterHead = htmlContent.substring(headIndex);
    
    return beforeHead + META_TAGS + '\n' + afterHead;
}

function processHtmlFile(filePath) {
    try {
        console.log(`📝 处理文件: ${filePath}`);
        
        // 读取HTML文件
        const htmlContent = fs.readFileSync(filePath, 'utf8');
        
        // 检查是否已经注入过
        if (htmlContent.includes('x-secure-context')) {
            console.log('✅ 文件已包含HTTPS标识，跳过');
            return;
        }
        
        // 注入meta标签
        const modifiedContent = injectMetaTags(htmlContent);
        
        // 写回文件
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        
        console.log('✅ HTTPS标识注入成功');
        
    } catch (error) {
        console.error(`❌ 处理文件失败: ${error.message}`);
    }
}

function findHtmlFiles(dir) {
    const htmlFiles = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.html')) {
                htmlFiles.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return htmlFiles;
}

function main() {
    console.log('🚀 开始注入HTTPS环境标识...');
    
    // 检查构建目录是否存在
    if (!fs.existsSync(BUILD_DIR)) {
        console.error(`❌ 构建目录不存在: ${BUILD_DIR}`);
        console.log('请先运行: cd web && npm run build');
        process.exit(1);
    }
    
    // 查找所有HTML文件
    const htmlFiles = findHtmlFiles(BUILD_DIR);
    
    if (htmlFiles.length === 0) {
        console.error('❌ 未找到HTML文件');
        process.exit(1);
    }
    
    console.log(`📁 找到 ${htmlFiles.length} 个HTML文件`);
    
    // 处理每个HTML文件
    htmlFiles.forEach(processHtmlFile);
    
    console.log('🎉 HTTPS环境标识注入完成！');
    console.log('');
    console.log('📋 注入的功能:');
    console.log('  ✅ X-Forwarded-Proto meta标签');
    console.log('  ✅ X-Secure-Context meta标签');
    console.log('  ✅ 全局变量 __SECURE_CONTEXT__');
    console.log('  ✅ 模拟 window.isSecureContext');
    console.log('  ✅ Web Crypto API环境检测');
    console.log('');
    console.log('🔧 现在可以部署到容器环境了！');
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    injectMetaTags,
    processHtmlFile,
    findHtmlFiles
};
