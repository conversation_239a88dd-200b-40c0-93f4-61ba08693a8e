#!/bin/bash

# HTTPS部署脚本
# 自动化部署前端应用到HTTPS环境

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== HTTPS环境部署脚本 ===${NC}"

# 配置变量
PROJECT_DIR="/usr/local/nginx/html"  # 请替换为您的项目路径
WEB_DIR="$PROJECT_DIR/web"
NGINX_CONF_DIR="/usr/local/nginx/sites-available"
NGINX_ENABLED_DIR="/usr/local/nginx/sites-enabled"
BUILD_DIR="$WEB_DIR/dist"

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}请不要使用root用户运行此脚本${NC}"
   exit 1
fi

# 步骤1：构建前端应用
echo -e "${BLUE}步骤1：构建前端应用${NC}"
cd $WEB_DIR

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}安装前端依赖...${NC}"
    npm install
fi

# 构建生产版本
echo -e "${YELLOW}构建生产版本...${NC}"
npm run build

if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}构建失败：找不到dist目录${NC}"
    exit 1
fi

echo -e "${GREEN}前端构建完成${NC}"

# 步骤2：配置nginx
echo -e "${BLUE}步骤2：配置nginx${NC}"

# 检查nginx是否安装
if ! command -v nginx &> /dev/null; then
    echo -e "${YELLOW}安装nginx...${NC}"
    sudo apt update
    sudo apt install -y nginx
fi

# 复制nginx配置
echo -e "${YELLOW}配置nginx...${NC}"
sudo cp nginx.conf $NGINX_CONF_DIR/xinhe-app

# 更新配置文件中的路径
sudo sed -i "s|/path/to/your/web/dist|$BUILD_DIR|g" $NGINX_CONF_DIR/xinhe-app

# 启用站点
sudo ln -sf $NGINX_CONF_DIR/xinhe-app $NGINX_ENABLED_DIR/

# 禁用默认站点
sudo rm -f $NGINX_ENABLED_DIR/default

# 测试nginx配置
echo -e "${YELLOW}测试nginx配置...${NC}"
if sudo nginx -t; then
    echo -e "${GREEN}nginx配置测试通过${NC}"
else
    echo -e "${RED}nginx配置测试失败${NC}"
    exit 1
fi

# 步骤3：SSL证书配置
echo -e "${BLUE}步骤3：SSL证书配置${NC}"

if [ ! -f "/etc/nginx/ssl/cert.pem" ] || [ ! -f "/etc/nginx/ssl/key.pem" ]; then
    echo -e "${YELLOW}SSL证书不存在，运行证书生成脚本...${NC}"
    chmod +x generate-ssl-cert.sh
    ./generate-ssl-cert.sh
else
    echo -e "${GREEN}SSL证书已存在${NC}"
fi

# 步骤4：启动服务
echo -e "${BLUE}步骤4：启动服务${NC}"

# 重启nginx
echo -e "${YELLOW}重启nginx...${NC}"
sudo systemctl restart nginx
sudo systemctl enable nginx

# 检查nginx状态
if sudo systemctl is-active --quiet nginx; then
    echo -e "${GREEN}nginx启动成功${NC}"
else
    echo -e "${RED}nginx启动失败${NC}"
    sudo systemctl status nginx
    exit 1
fi

# 启动后端服务（如果需要）
echo -e "${YELLOW}启动后端服务...${NC}"
cd $PROJECT_DIR/server

# 检查是否有PM2
if command -v pm2 &> /dev/null; then
    pm2 restart all || pm2 start npm --name "xinhe-backend" -- start
else
    echo -e "${YELLOW}建议安装PM2来管理后端服务：npm install -g pm2${NC}"
    echo -e "${YELLOW}手动启动后端服务：cd server && npm start${NC}"
fi

# 步骤5：防火墙配置
echo -e "${BLUE}步骤5：防火墙配置${NC}"

if command -v ufw &> /dev/null; then
    echo -e "${YELLOW}配置防火墙...${NC}"
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    sudo ufw allow 22/tcp  # SSH
    echo -e "${GREEN}防火墙配置完成${NC}"
else
    echo -e "${YELLOW}未检测到ufw，请手动配置防火墙开放80和443端口${NC}"
fi

# 步骤6：验证部署
echo -e "${BLUE}步骤6：验证部署${NC}"

SERVER_IP="*************"
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 5

# 检查HTTP重定向
echo -e "${YELLOW}检查HTTP重定向...${NC}"
if curl -s -I "http://$SERVER_IP" | grep -q "301\|302"; then
    echo -e "${GREEN}HTTP重定向配置正确${NC}"
else
    echo -e "${YELLOW}HTTP重定向可能未配置${NC}"
fi

# 检查HTTPS
echo -e "${YELLOW}检查HTTPS访问...${NC}"
if curl -k -s -I "https://$SERVER_IP" | grep -q "200"; then
    echo -e "${GREEN}HTTPS访问正常${NC}"
else
    echo -e "${RED}HTTPS访问异常${NC}"
fi

echo -e "${GREEN}=== 部署完成 ===${NC}"
echo -e "${YELLOW}访问地址：${NC}"
echo -e "  HTTP:  http://$SERVER_IP (自动重定向到HTTPS)"
echo -e "  HTTPS: https://$SERVER_IP"
echo ""
echo -e "${YELLOW}注意事项：${NC}"
echo "1. 如果使用自签名证书，浏览器会显示安全警告"
echo "2. 点击'高级'然后'继续访问'即可"
echo "3. 生产环境建议使用有效的SSL证书"
echo "4. 确保Keycloak服务器的客户端配置包含新的重定向URI"

echo -e "${BLUE}Keycloak客户端配置更新：${NC}"
echo "重定向URI需要添加："
echo "  https://$SERVER_IP/auth/callback"
echo "Web Origins需要添加："
echo "  https://$SERVER_IP"
