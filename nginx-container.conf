# Nginx配置文件 - 容器化部署专用
# 使用HTTP端口但通过X-Forwarded-Proto头模拟HTTPS环境
# 适用于容器内部运行，外部负载均衡器处理SSL

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /var/run/nginx.pid;

# 包含模块配置
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '"$http_x_forwarded_proto" "$http_x_real_ip"';

    access_log /var/log/nginx/access.log main;

    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # MIME类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 主服务器配置 - 监听8088端口
    server {
        listen 8088;
        server_name ************* localhost;  # 添加localhost支持容器内访问
        
        # 设置真实IP（如果有负载均衡器）
        real_ip_header X-Forwarded-For;
        set_real_ip_from 10.0.0.0/8;
        set_real_ip_from **********/12;
        set_real_ip_from ***********/16;
        real_ip_recursive on;

        # 安全头配置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;

        # 前端静态文件服务
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            index index.html index.htm;

            # 设置HTTPS协议头 - 关键配置，让前端应用认为运行在HTTPS环境
            add_header X-Forwarded-Proto "https" always;
            add_header X-Forwarded-Port "443" always;
            add_header X-Forwarded-Ssl "on" always;
            add_header X-Secure-Context "true" always;
            
            # 静态资源缓存
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                add_header X-Forwarded-Proto "https" always;
                add_header X-Forwarded-Port "443" always;
                add_header X-Forwarded-Ssl "on" always;
                add_header X-Secure-Context "true" always;
            }

            # HTML文件不缓存
            location ~* \.(html|htm)$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header X-Forwarded-Proto "https" always;
                add_header X-Forwarded-Port "443" always;
                add_header X-Forwarded-Ssl "on" always;
                add_header X-Secure-Context "true" always;
            }
        }

        # API代理到后端服务
        location /api/ {
            # 后端服务地址 - 根据您的实际情况调整
            proxy_pass http://127.0.0.1:9021;
            
            # 基本代理头
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            # 关键：设置HTTPS协议头
            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-Port 443;
            proxy_set_header X-Forwarded-Ssl on;
            
            # 缓存控制
            proxy_cache_bypass $http_upgrade;
            proxy_no_cache $http_upgrade;
            
            # 超时配置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # 缓冲配置
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }

        # WebSocket支持
        location /socket.io/ {
            proxy_pass http://127.0.0.1:8099;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            # WebSocket也需要HTTPS头
            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-Port 443;
            proxy_set_header X-Forwarded-Ssl on;
            
            # WebSocket特定配置
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }

        # 健康检查端点
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
            add_header X-Forwarded-Proto https;
        }

        # 禁止访问隐藏文件
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # 禁止访问备份文件
        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # 错误页面
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
            add_header X-Forwarded-Proto https;
        }
    }

    # 如果需要支持多个域名或环境
    # server {
    #     listen 80;
    #     server_name your-other-domain.com;
    #     
    #     # 相同的配置...
    # }
}
