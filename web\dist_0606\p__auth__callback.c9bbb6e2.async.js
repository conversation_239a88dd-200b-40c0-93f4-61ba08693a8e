"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7647],{20926:function(ie,_,n){n.r(_);var l=n(15009),x=n.n(l),W=n(99289),Z=n.n(W),z=n(5574),U=n.n(z),A=n(67294),B=n(40056),H=n(74330),T=n(84226),k=n(18883),p=n(85893),G=function(){var X=(0,A.useState)(null),w=U()(X,2),C=w[0],R=w[1];return(0,A.useEffect)(function(){var Q=function(){var J=Z()(x()().mark(function L(){var h,D,N,F,$,K,S;return x()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(a.prev=0,console.log("=== \u5904\u7406Keycloak\u56DE\u8C03\u5F00\u59CB ==="),console.log("\u5F53\u524DURL:",window.location.href),h=new URLSearchParams(window.location.hash.substring(1)),D=h.get("code"),N=h.get("state"),F=h.get("session_state"),$=h.get("error"),K=h.get("error_description"),console.log("\u56DE\u8C03\u53C2\u6570:",{code:D?"\u5B58\u5728":"\u4E0D\u5B58\u5728",state:N,sessionState:F,error:$,errorDescription:K}),!$){a.next=12;break}throw new Error("Keycloak\u9519\u8BEF: ".concat($," - ").concat(K));case 12:if(!D){a.next=33;break}return console.log("\u68C0\u6D4B\u5230\u6388\u6743\u7801\uFF0C\u5F00\u59CB\u521D\u59CB\u5316Keycloak"),a.prev=14,a.next=17,k.ZP.init({onLoad:"login-required",checkLoginIframe:!1,redirectUri:window.location.origin+"/auth/callback",silentCheckSsoRedirectUri:window.location.origin+"/silent-check-sso.html",pkceMethod:"S256"});case 17:if(S=a.sent,console.log("Keycloak\u521D\u59CB\u5316\u7ED3\u679C:",S),!S){a.next=24;break}console.log("Keycloak\u8BA4\u8BC1\u6210\u529F\uFF0C\u91CD\u5B9A\u5411\u5230\u4E3B\u9875"),window.location.replace("/Console/projects"),a.next=25;break;case 24:throw new Error("Keycloak\u8BA4\u8BC1\u5931\u8D25");case 25:a.next=31;break;case 27:throw a.prev=27,a.t0=a.catch(14),console.error("Keycloak\u521D\u59CB\u5316\u5931\u8D25:",a.t0),new Error("Keycloak\u521D\u59CB\u5316\u5931\u8D25: ".concat(a.t0.message));case 31:a.next=36;break;case 33:console.log("\u6CA1\u6709\u6388\u6743\u7801\uFF0C\u91CD\u5B9A\u5411\u5230\u767B\u5F55\u9875\u9762"),R("\u65E0\u6548\u7684\u56DE\u8C03\u8BBF\u95EE\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"),setTimeout(function(){T.history.replace("/user/login")},2e3);case 36:a.next=43;break;case 38:a.prev=38,a.t1=a.catch(0),console.error("\u56DE\u8C03\u5904\u7406\u5931\u8D25:",a.t1),R("\u56DE\u8C03\u5904\u7406\u5931\u8D25: ".concat(a.t1.message||"\u672A\u77E5\u9519\u8BEF")),setTimeout(function(){T.history.replace("/user/login")},3e3);case 43:case"end":return a.stop()}},L,null,[[0,38],[14,27]])}));return function(){return J.apply(this,arguments)}}();Q()},[]),(0,p.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",padding:"20px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white"},children:C?(0,p.jsx)(B.Z,{message:"\u8BA4\u8BC1\u9519\u8BEF",description:C,type:"error",showIcon:!0,style:{marginBottom:16}}):(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(H.Z,{size:"large",style:{color:"white"}}),(0,p.jsx)("div",{style:{marginTop:24,fontSize:"18px",fontWeight:500,textAlign:"center"},children:"\u767B\u5F55\u6210\u529F\uFF01\u6B63\u5728\u8DF3\u8F6C..."}),(0,p.jsx)("div",{style:{marginTop:8,fontSize:"14px",opacity:.8,textAlign:"center"},children:"\u8BF7\u7A0D\u5019\uFF0C\u5373\u5C06\u8FDB\u5165\u7CFB\u7EDF"})]})})};_.default=G},40056:function(ie,_,n){n.d(_,{Z:function(){return pe}});var l=n(67294),x=n(19735),W=n(17012),Z=n(62208),z=n(29950),U=n(1558),A=n(93967),B=n.n(A),H=n(29372),T=n(64217),k=n(42550),p=n(96159),G=n(53124),V=n(11568),X=n(14747),w=n(83559);const C=(e,o,t,r,s)=>({background:e,border:`${(0,V.bf)(r.lineWidth)} ${r.lineType} ${o}`,[`${s}-icon`]:{color:t}}),R=e=>{const{componentCls:o,motionDurationSlow:t,marginXS:r,marginSM:s,fontSize:i,fontSizeLG:u,lineHeight:m,borderRadiusLG:g,motionEaseInOutCirc:f,withDescriptionIconSize:y,colorText:E,colorTextHeading:j,withDescriptionPadding:O,defaultPadding:c}=e;return{[o]:Object.assign(Object.assign({},(0,X.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:c,wordWrap:"break-word",borderRadius:g,[`&${o}-rtl`]:{direction:"rtl"},[`${o}-content`]:{flex:1,minWidth:0},[`${o}-icon`]:{marginInlineEnd:r,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:m},"&-message":{color:j},[`&${o}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${t} ${f}, opacity ${t} ${f},
        padding-top ${t} ${f}, padding-bottom ${t} ${f},
        margin-bottom ${t} ${f}`},[`&${o}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${o}-with-description`]:{alignItems:"flex-start",padding:O,[`${o}-icon`]:{marginInlineEnd:s,fontSize:y,lineHeight:0},[`${o}-message`]:{display:"block",marginBottom:r,color:j,fontSize:u},[`${o}-description`]:{display:"block",color:E}},[`${o}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},Q=e=>{const{componentCls:o,colorSuccess:t,colorSuccessBorder:r,colorSuccessBg:s,colorWarning:i,colorWarningBorder:u,colorWarningBg:m,colorError:g,colorErrorBorder:f,colorErrorBg:y,colorInfo:E,colorInfoBorder:j,colorInfoBg:O}=e;return{[o]:{"&-success":C(s,r,t,e,o),"&-info":C(O,j,E,e,o),"&-warning":C(m,u,i,e,o),"&-error":Object.assign(Object.assign({},C(y,f,g,e,o)),{[`${o}-description > pre`]:{margin:0,padding:0}})}}},J=e=>{const{componentCls:o,iconCls:t,motionDurationMid:r,marginXS:s,fontSizeIcon:i,colorIcon:u,colorIconHover:m}=e;return{[o]:{"&-action":{marginInlineStart:s},[`${o}-close-icon`]:{marginInlineStart:s,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,V.bf)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${t}-close`]:{color:u,transition:`color ${r}`,"&:hover":{color:m}}},"&-close-text":{color:u,transition:`color ${r}`,"&:hover":{color:m}}}}},L=e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`});var h=(0,w.I$)("Alert",e=>[R(e),Q(e),J(e)],L),D=function(e,o){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&o.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)o.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(t[r[s]]=e[r[s]]);return t};const N={success:x.Z,info:U.Z,error:W.Z,warning:z.Z},F=e=>{const{icon:o,prefixCls:t,type:r}=e,s=N[r]||null;return o?(0,p.wm)(o,l.createElement("span",{className:`${t}-icon`},o),()=>({className:B()(`${t}-icon`,o.props.className)})):l.createElement(s,{className:`${t}-icon`})},$=e=>{const{isClosable:o,prefixCls:t,closeIcon:r,handleClose:s,ariaProps:i}=e,u=r===!0||r===void 0?l.createElement(Z.Z,null):r;return o?l.createElement("button",Object.assign({type:"button",onClick:s,className:`${t}-close-icon`,tabIndex:0},i),u):null};var S=l.forwardRef((e,o)=>{const{description:t,prefixCls:r,message:s,banner:i,className:u,rootClassName:m,style:g,onMouseEnter:f,onMouseLeave:y,onClick:E,afterClose:j,showIcon:O,closable:c,closeText:M,closeIcon:b,action:oe,id:ge}=e,ve=D(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[ne,he]=l.useState(!1),te=l.useRef(null);l.useImperativeHandle(o,()=>({nativeElement:te.current}));const{getPrefixCls:Ce,direction:ye,closable:I,closeIcon:re,className:Ee,style:be}=(0,G.dj)("alert"),d=Ce("alert",r),[Ie,De,$e]=h(d),Se=v=>{var P;he(!0),(P=e.onClose)===null||P===void 0||P.call(e,v)},se=l.useMemo(()=>e.type!==void 0?e.type:i?"warning":"info",[e.type,i]),je=l.useMemo(()=>typeof c=="object"&&c.closeIcon||M?!0:typeof c=="boolean"?c:b!==!1&&b!==null&&b!==void 0?!0:!!I,[M,b,c,I]),ae=i&&O===void 0?!0:O,Oe=B()(d,`${d}-${se}`,{[`${d}-with-description`]:!!t,[`${d}-no-icon`]:!ae,[`${d}-banner`]:!!i,[`${d}-rtl`]:ye==="rtl"},Ee,u,m,$e,De),Pe=(0,T.Z)(ve,{aria:!0,data:!0}),Be=l.useMemo(()=>typeof c=="object"&&c.closeIcon?c.closeIcon:M||(b!==void 0?b:typeof I=="object"&&I.closeIcon?I.closeIcon:re),[b,c,M,re]),Me=l.useMemo(()=>{const v=c!=null?c:I;if(typeof v=="object"){const{closeIcon:P}=v;return D(v,["closeIcon"])}return{}},[c,I]);return Ie(l.createElement(H.ZP,{visible:!ne,motionName:`${d}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:v=>({maxHeight:v.offsetHeight}),onLeaveEnd:j},({className:v,style:P},le)=>l.createElement("div",Object.assign({id:ge,ref:(0,k.sQ)(te,le),"data-show":!ne,className:B()(Oe,v),style:Object.assign(Object.assign(Object.assign({},be),g),P),onMouseEnter:f,onMouseLeave:y,onClick:E,role:"alert"},Pe),ae?l.createElement(F,{description:t,icon:e.icon,prefixCls:d,type:se}):null,l.createElement("div",{className:`${d}-content`},s?l.createElement("div",{className:`${d}-message`},s):null,t?l.createElement("div",{className:`${d}-description`},t):null),oe?l.createElement("div",{className:`${d}-action`},oe):null,l.createElement($,{isClosable:je,prefixCls:d,closeIcon:Be,handleClose:Se,ariaProps:Me}))))}),Y=n(15671),a=n(43144),q=n(61120),ce=n(78814),de=n(82963);function ue(e,o,t){return o=(0,q.Z)(o),(0,de.Z)(e,(0,ce.Z)()?Reflect.construct(o,t||[],(0,q.Z)(e).constructor):o.apply(e,t))}var me=n(60136),fe=function(e){function o(){var t;return(0,Y.Z)(this,o),t=ue(this,o,arguments),t.state={error:void 0,info:{componentStack:""}},t}return(0,me.Z)(o,e),(0,a.Z)(o,[{key:"componentDidCatch",value:function(r,s){this.setState({error:r,info:s})}},{key:"render",value:function(){const{message:r,description:s,id:i,children:u}=this.props,{error:m,info:g}=this.state,f=(g==null?void 0:g.componentStack)||null,y=typeof r=="undefined"?(m||"").toString():r,E=typeof s=="undefined"?f:s;return m?l.createElement(S,{id:i,type:"error",message:y,description:l.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},E)}):u}}])}(l.Component);const ee=S;ee.ErrorBoundary=fe;var pe=ee}}]);
