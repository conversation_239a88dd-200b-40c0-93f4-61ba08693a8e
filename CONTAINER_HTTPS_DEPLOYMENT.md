# 容器化HTTPS部署指南

## 概述

本指南提供了一个完整的解决方案，使用HTTP端口但通过`X-Forwarded-Proto`头模拟HTTPS环境，解决Web Crypto API不可用的问题。

## 核心原理

### 问题分析
- **Web Crypto API限制**：只在安全上下文（HTTPS或localhost）中可用
- **容器化挑战**：不想暴露443端口，只使用HTTP端口
- **Keycloak PKCE要求**：需要Web Crypto API支持

### 解决方案
1. **nginx配置**：添加`X-Forwarded-Proto: https`头
2. **HTML注入**：在页面中添加meta标签和JavaScript
3. **前端检测**：动态检测并启用Web Crypto API支持

## 文件说明

### 核心配置文件

1. **`nginx-container.conf`** - nginx配置文件
   - 监听80端口
   - 添加HTTPS模拟头
   - 代理后端API

2. **`inject-https-meta.js`** - HTML注入脚本
   - 添加meta标签
   - 设置全局变量
   - 模拟安全上下文

3. **`quick-deploy.sh`** - 快速部署脚本
   - 一键构建和部署
   - 自动容器管理

## 快速开始

### 方法1：8088端口专用部署（推荐）

```bash
# 1. 给脚本执行权限
chmod +x deploy-8088.sh

# 2. 一键部署到8088端口
./deploy-8088.sh
```

### 方法2：快速部署

```bash
# 1. 给脚本执行权限
chmod +x quick-deploy.sh

# 2. 一键部署
./quick-deploy.sh
```

### 方法2：完整部署

```bash
# 1. 给脚本执行权限
chmod +x deploy-container-https.sh

# 2. 完整部署
./deploy-container-https.sh
```

### 方法4：手动部署（8088端口）

```bash
# 1. 构建前端
cd web && npm run build && cd ..

# 2. 注入HTTPS标识
node inject-https-meta.js

# 3. 启动容器（注意端口映射8088:8088）
docker run -d \
  --name xinhe-nginx-8088 \
  -p 8088:8088 \
  -v $(pwd)/nginx-container.conf:/etc/nginx/nginx.conf:ro \
  -v $(pwd)/web/dist:/usr/share/nginx/html:ro \
  --add-host host.docker.internal:host-gateway \
  nginx:alpine
```

## 配置详解

### nginx配置关键点

```nginx
# 添加HTTPS模拟头
add_header X-Forwarded-Proto "https" always;
add_header X-Forwarded-Port "443" always;
add_header X-Forwarded-Ssl "on" always;
add_header X-Secure-Context "true" always;

# API代理配置
location /api/ {
    proxy_pass http://host.docker.internal:9021;
    proxy_set_header X-Forwarded-Proto https;
    # ... 其他代理配置
}
```

### HTML注入内容

```html
<!-- meta标签 -->
<meta name="x-forwarded-proto" content="https">
<meta name="x-secure-context" content="true">

<!-- JavaScript模拟 -->
<script>
    window.__SECURE_CONTEXT__ = true;
    Object.defineProperty(window, 'isSecureContext', {
        value: true,
        writable: false
    });
</script>
```

### 前端检测逻辑

```javascript
// 检测HTTPS环境或模拟环境
const isSecureContext = () => {
    // 检查真实HTTPS
    if (window.location.protocol === 'https:') return true;
    
    // 检查localhost
    if (window.location.hostname === 'localhost') return true;
    
    // 检查模拟环境
    if (window.__SECURE_CONTEXT__ === true) return true;
    
    return false;
};
```

## Keycloak配置

### 客户端配置更新

1. **登录Keycloak管理控制台**
   ```
   https://*************:9088/admin
   ```

2. **更新重定向URI**
   ```
   http://*************:8088/auth/callback
   http://*************:8088/*
   ```

3. **更新Web Origins**
   ```
   http://*************:8088
   ```

### 验证配置

访问应用后点击"环境诊断"，应该看到：
```
✅ Web Crypto API - Web Crypto API 可用
✅ 安全上下文 - 当前运行在安全上下文中
✅ Keycloak连接 - Keycloak服务器连接正常
```

## 管理命令

### 容器管理

```bash
# 查看容器状态
docker ps | grep xinhe-nginx-https

# 查看日志
docker logs -f xinhe-nginx-https

# 重启容器
docker restart xinhe-nginx-https

# 停止容器
docker stop xinhe-nginx-https

# 删除容器
docker rm xinhe-nginx-https

# 进入容器
docker exec -it xinhe-nginx-https sh
```

### 更新应用

```bash
# 重新构建和部署
./quick-deploy.sh

# 或者手动更新
cd web && npm run build && cd ..
node inject-https-meta.js
docker restart xinhe-nginx-https
```

## 故障排除

### 常见问题

1. **Web Crypto API仍然不可用**
   - 检查HTML是否正确注入了meta标签
   - 查看浏览器控制台是否有错误
   - 确认nginx头配置正确

2. **Keycloak连接失败**
   - 检查Keycloak服务器是否运行
   - 验证客户端配置的重定向URI
   - 查看网络连接

3. **容器启动失败**
   - 检查端口是否被占用：`netstat -tlnp | grep 8088`
   - 查看容器日志：`docker logs xinhe-nginx-https`
   - 验证nginx配置：`docker exec xinhe-nginx-https nginx -t`

### 调试步骤

1. **检查环境注入**
   ```bash
   # 查看HTML文件是否包含meta标签
   grep -n "x-secure-context" web/dist/index.html
   ```

2. **测试nginx配置**
   ```bash
   # 检查响应头
   curl -I http://*************:8088
   
   # 应该看到：
   # X-Forwarded-Proto: https
   # X-Secure-Context: true
   ```

3. **验证API代理**
   ```bash
   # 测试API访问
   curl -I http://*************:8088/api/health
   ```

## 技术原理

### HTTPS模拟机制

1. **服务器层面**：nginx添加HTTPS相关头
2. **HTML层面**：meta标签提供环境标识
3. **JavaScript层面**：全局变量和属性模拟
4. **应用层面**：Keycloak检测并启用PKCE

### 安全考虑

- **开发/测试环境**：此方案适用
- **生产环境**：建议使用真实HTTPS
- **数据传输**：仍使用HTTP，注意敏感数据保护

## 性能优化

### nginx优化

```nginx
# 启用gzip压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript;

# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 容器优化

```bash
# 使用更小的镜像
nginx:alpine

# 限制容器资源
docker run --memory=512m --cpus=1 ...
```

## 总结

这个解决方案通过以下方式解决了Web Crypto API问题：

1. ✅ **无需真实HTTPS证书**
2. ✅ **容器化部署友好**
3. ✅ **Web Crypto API可用**
4. ✅ **Keycloak PKCE支持**
5. ✅ **简单易维护**

适用于开发、测试和内网部署环境。
