# Nginx配置文件 - 支持HTTPS和前端应用
# 请根据您的实际情况调整域名和路径

# HTTP服务器 - 重定向到HTTPS
server {
    listen 8088;
    server_name *************;  # 替换为您的域名或IP
    
    # 重定向所有HTTP请求到HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS服务器 - 主要配置
server {
    listen 443 ssl http2;
    server_name *************;  # 替换为您的域名或IP
    
    # SSL证书配置
    ssl_certificate /usr/local/nginx/ssl/cert.pem;        # 证书文件路径
    ssl_certificate_key /usr/local/nginx/ssl/key.pem;     # 私钥文件路径
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 前端静态文件
    location / {
        root html/dist_keycloak;  # 替换为您的前端构建目录
        try_files $uri $uri/ /index.html;
        index index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理到后端服务器
    location /api/ {
        proxy_pass http://*************:9084;  # 您的后端服务端口
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root html;
    }
}

# 如果您有多个域名或子域名，可以添加额外的server块
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#     
#     ssl_certificate /etc/nginx/ssl/your-domain.pem;
#     ssl_certificate_key /etc/nginx/ssl/your-domain-key.pem;
#     
#     # 其他配置...
# }
