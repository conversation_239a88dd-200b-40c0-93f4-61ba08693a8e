#!/bin/bash

# 容器化HTTPS部署脚本
# 使用HTTP端口但通过X-Forwarded-Proto模拟HTTPS环境

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 容器化HTTPS环境部署 ===${NC}"

# 配置变量
PROJECT_DIR=$(pwd)
WEB_DIR="$PROJECT_DIR/web"
CONTAINER_NAME="xinhe-nginx-https"
HTTP_PORT="8088"  # 外部访问端口
BACKEND_PORT="9021"
WEBSOCKET_PORT="8099"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker未安装，请先安装Docker${NC}"
    exit 1
fi

# 停止现有容器
echo -e "${YELLOW}停止现有容器...${NC}"
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# 构建前端应用
echo -e "${BLUE}步骤1：构建前端应用${NC}"
if [ -d "$WEB_DIR" ]; then
    cd $WEB_DIR
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}安装前端依赖...${NC}"
        npm install
    fi
    
    # 构建
    echo -e "${YELLOW}构建生产版本...${NC}"
    npm run build

    if [ ! -d "dist" ]; then
        echo -e "${RED}构建失败：找不到dist目录${NC}"
        exit 1
    fi

    echo -e "${GREEN}前端构建完成${NC}"
    cd $PROJECT_DIR

    # 注入HTTPS环境标识
    echo -e "${YELLOW}注入HTTPS环境标识...${NC}"
    node inject-https-meta.js
    echo -e "${GREEN}HTTPS标识注入完成${NC}"
else
    echo -e "${RED}未找到web目录${NC}"
    exit 1
fi

# 创建容器目录结构
echo -e "${BLUE}步骤2：准备容器配置${NC}"
mkdir -p ./docker-deploy/html
mkdir -p ./docker-deploy/conf

# 复制前端文件
echo -e "${YELLOW}复制前端文件...${NC}"
cp -r $WEB_DIR/dist/* ./docker-deploy/html/

# 复制nginx配置
echo -e "${YELLOW}配置nginx...${NC}"
cp nginx-container.conf ./docker-deploy/conf/nginx.conf

# 更新配置中的后端地址（容器内访问宿主机）
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux环境
    sed -i "s|proxy_pass http://127.0.0.1:9021|proxy_pass http://host.docker.internal:$BACKEND_PORT|g" ./docker-deploy/conf/nginx.conf
    sed -i "s|proxy_pass http://127.0.0.1:8099|proxy_pass http://host.docker.internal:$WEBSOCKET_PORT|g" ./docker-deploy/conf/nginx.conf
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS环境
    sed -i '' "s|proxy_pass http://127.0.0.1:9021|proxy_pass http://host.docker.internal:$BACKEND_PORT|g" ./docker-deploy/conf/nginx.conf
    sed -i '' "s|proxy_pass http://127.0.0.1:8099|proxy_pass http://host.docker.internal:$WEBSOCKET_PORT|g" ./docker-deploy/conf/nginx.conf
fi

# 创建Dockerfile
cat > ./docker-deploy/Dockerfile <<EOF
FROM nginx:alpine

# 复制配置文件
COPY conf/nginx.conf /etc/nginx/nginx.conf

# 复制前端文件
COPY html/ /usr/share/nginx/html/

# 创建日志目录
RUN mkdir -p /var/log/nginx

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html
RUN chmod -R 755 /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
EOF

# 创建docker-compose.yml
cat > ./docker-deploy/docker-compose.yml <<EOF
version: '3.8'

services:
  nginx:
    build: .
    container_name: $CONTAINER_NAME
    ports:
      - "$HTTP_PORT:80"
    volumes:
      - ./conf/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./html:/usr/share/nginx/html:ro
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NGINX_ENVSUBST_TEMPLATE_SUFFIX=.template
    networks:
      - xinhe-network

networks:
  xinhe-network:
    driver: bridge
EOF

# 构建并启动容器
echo -e "${BLUE}步骤3：构建并启动容器${NC}"
cd ./docker-deploy

echo -e "${YELLOW}构建Docker镜像...${NC}"
docker-compose build

echo -e "${YELLOW}启动容器...${NC}"
docker-compose up -d

# 等待容器启动
echo -e "${YELLOW}等待容器启动...${NC}"
sleep 10

# 验证容器状态
if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}✅ 容器启动成功${NC}"
else
    echo -e "${RED}❌ 容器启动失败${NC}"
    echo -e "${YELLOW}查看日志：${NC}"
    docker-compose logs
    exit 1
fi

# 验证服务
echo -e "${BLUE}步骤4：验证服务${NC}"

echo -e "${YELLOW}检查HTTP访问...${NC}"
sleep 5
if curl -s -I "http://*************:$HTTP_PORT" | grep -q "200\|301\|302"; then
    echo -e "${GREEN}✅ HTTP访问正常${NC}"
else
    echo -e "${RED}❌ HTTP访问异常${NC}"
    echo -e "${YELLOW}检查容器日志：${NC}"
    docker-compose logs --tail=20
fi

# 检查X-Forwarded-Proto头
echo -e "${YELLOW}检查HTTPS模拟...${NC}"
RESPONSE=$(curl -s -I "http://*************:$HTTP_PORT" || echo "")
if echo "$RESPONSE" | grep -q "200\|301\|302"; then
    echo -e "${GREEN}✅ 服务响应正常${NC}"
else
    echo -e "${YELLOW}⚠️ 服务响应需要检查${NC}"
fi

cd $PROJECT_DIR

echo -e "${GREEN}=== 部署完成 ===${NC}"
echo -e "${YELLOW}访问信息：${NC}"
echo "  应用地址: http://*************:$HTTP_PORT"
echo "  容器名称: $CONTAINER_NAME"
echo "  HTTP端口: $HTTP_PORT"
echo ""
echo -e "${YELLOW}管理命令：${NC}"
echo "  查看状态: cd docker-deploy && docker-compose ps"
echo "  查看日志: cd docker-deploy && docker-compose logs -f"
echo "  重启服务: cd docker-deploy && docker-compose restart"
echo "  停止服务: cd docker-deploy && docker-compose down"
echo "  进入容器: docker exec -it $CONTAINER_NAME sh"
echo ""
echo -e "${YELLOW}重要说明：${NC}"
echo "1. 应用通过X-Forwarded-Proto头模拟HTTPS环境"
echo "2. Web Crypto API应该在此配置下可用"
echo "3. 请更新Keycloak客户端配置："
echo "   - 重定向URI: http://*************:$HTTP_PORT/auth/callback"
echo "   - Web Origins: http://*************:$HTTP_PORT"
echo "4. 如需更新前端，重新运行此脚本即可"
echo ""
echo -e "${BLUE}测试命令：${NC}"
echo "  curl -I http://*************:$HTTP_PORT"
echo "  curl -I http://*************:$HTTP_PORT/api/health"
