# Keycloak客户端配置指南

## 概述

本指南帮助您配置Keycloak客户端以支持HTTPS环境下的前端应用。

## 配置步骤

### 1. 登录Keycloak管理控制台

访问：`https://*************:9088/admin`

### 2. 选择Realm

选择 `dev_xh_key` realm

### 3. 配置客户端

#### 3.1 基本设置

1. 进入 `Clients` → 选择 `sulei_01`
2. 在 `Settings` 标签页中配置：

```
Client ID: sulei_01
Client Protocol: openid-connect
Access Type: public
Standard Flow Enabled: ON
Implicit Flow Enabled: OFF
Direct Access Grants Enabled: ON
Service Accounts Enabled: OFF
Authorization Enabled: OFF
```

#### 3.2 重定向URI配置

在 `Valid Redirect URIs` 中添加：
```
https://*************/auth/callback
https://*************/*
http://localhost:3000/auth/callback
http://localhost:3000/*
```

#### 3.3 Web Origins配置

在 `Web Origins` 中添加：
```
https://*************
http://localhost:3000
```

#### 3.4 高级设置

在 `Advanced Settings` 中：
```
Access Token Lifespan: 5 Minutes
SSO Session Idle: 30 Minutes
SSO Session Max: 10 Hours
Client Session Idle: 30 Minutes
Client Session Max: 10 Hours
```

### 4. CORS配置

确保以下设置正确：
```
Web Origins: https://*************
Admin URL: (留空)
```

### 5. 客户端作用域配置

#### 5.1 默认客户端作用域

确保包含以下作用域：
- `openid`
- `profile`
- `email`
- `roles`

#### 5.2 可选客户端作用域

可以添加：
- `address`
- `phone`

### 6. 角色映射

#### 6.1 Realm角色

确保以下角色存在：
- `owner`
- `admin`
- `maintainer`
- `user`

#### 6.2 客户端角色

如果需要，可以创建客户端特定的角色。

### 7. 用户配置

#### 7.1 创建测试用户

1. 进入 `Users` → `Add user`
2. 填写用户信息：
   ```
   Username: testuser
   Email: <EMAIL>
   First Name: Test
   Last Name: User
   Email Verified: ON
   Enabled: ON
   ```

#### 7.2 设置密码

1. 进入用户详情 → `Credentials` 标签页
2. 设置密码，取消 `Temporary` 选项

#### 7.3 分配角色

1. 进入用户详情 → `Role Mappings` 标签页
2. 分配适当的realm角色

### 8. 安全设置

#### 8.1 Realm设置

在 `Realm Settings` → `Security Defenses` 中：
```
Brute Force Detection: ON
Max Login Failures: 5
Wait Increment: 60 seconds
Quick Login Check: 1000 milliseconds
Minimum Quick Login Wait: 60 seconds
Max Wait: 900 seconds
Failure Reset Time: 12 hours
```

#### 8.2 SSL设置

在 `Realm Settings` → `General` 中：
```
Require SSL: External requests
```

### 9. 事件配置

#### 9.1 事件监听

在 `Events` → `Config` 中启用：
```
Save Events: ON
Expiration: 7 days
```

选择要记录的事件类型：
- `LOGIN`
- `LOGIN_ERROR`
- `LOGOUT`
- `REGISTER`

### 10. 测试配置

#### 10.1 测试重定向

访问以下URL测试重定向：
```
https://*************/auth/callback?code=test
```

#### 10.2 测试登录流程

1. 访问：`https://*************`
2. 点击"使用Keycloak登录"
3. 应该重定向到Keycloak登录页面
4. 登录后应该重定向回应用

### 11. 故障排除

#### 11.1 常见问题

**问题1：重定向URI不匹配**
- 检查客户端配置中的重定向URI
- 确保包含正确的协议（https）和端口

**问题2：CORS错误**
- 检查Web Origins配置
- 确保包含前端应用的完整URL

**问题3：SSL证书问题**
- 确保Keycloak服务器使用有效的SSL证书
- 或者在客户端配置中允许自签名证书

#### 11.2 调试工具

使用浏览器开发者工具：
1. 打开Network标签页
2. 查看Keycloak相关的网络请求
3. 检查响应状态码和错误信息

#### 11.3 日志查看

查看Keycloak服务器日志：
```bash
# 如果使用Docker
docker logs keycloak-container

# 如果使用standalone部署
tail -f /opt/keycloak/standalone/log/server.log
```

### 12. 生产环境建议

#### 12.1 安全建议

1. 使用有效的SSL证书（Let's Encrypt或商业证书）
2. 定期更新Keycloak版本
3. 启用双因素认证
4. 配置适当的会话超时
5. 启用审计日志

#### 12.2 性能优化

1. 配置数据库连接池
2. 启用缓存
3. 配置负载均衡（如果有多个实例）
4. 监控系统资源使用情况

#### 12.3 备份策略

1. 定期备份Keycloak数据库
2. 备份配置文件
3. 备份SSL证书
4. 测试恢复流程

## 配置验证清单

- [ ] 客户端基本设置正确
- [ ] 重定向URI包含HTTPS地址
- [ ] Web Origins配置正确
- [ ] 用户角色分配正确
- [ ] SSL设置启用
- [ ] 测试登录流程成功
- [ ] CORS配置无错误
- [ ] 事件日志记录正常

## 联系支持

如果遇到问题，请检查：
1. Keycloak服务器日志
2. 浏览器开发者工具
3. 网络连接状态
4. SSL证书有效性
