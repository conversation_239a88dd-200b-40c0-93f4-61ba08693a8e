"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7019],{36688:function(et,ge){var d={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"};ge.Z=d},47019:function(et,ge,d){d.d(ge,{Z:function(){return hn}});var R=d(65223),J=d(74902),l=d(67294),tt=d(93967),ne=d.n(tt),Ie=d(29372),Se=d(33603),pe=d(35792);function me(e){const[t,n]=l.useState(e);return l.useEffect(()=>{const r=setTimeout(()=>{n(e)},e.length?0:10);return()=>{clearTimeout(r)}},[e]),t}var _=d(11568),Oe=d(14747),Ee=d(50438),nt=d(33507),rt=d(83262),Fe=d(83559),ot=e=>{const{componentCls:t}=e,n=`${t}-show-help`,r=`${t}-show-help-item`;return{[n]:{transition:`opacity ${e.motionDurationFast} ${e.motionEaseInOut}`,"&-appear, &-enter":{opacity:0,"&-active":{opacity:1}},"&-leave":{opacity:1,"&-active":{opacity:0}},[r]:{overflow:"hidden",transition:`height ${e.motionDurationFast} ${e.motionEaseInOut},
                     opacity ${e.motionDurationFast} ${e.motionEaseInOut},
                     transform ${e.motionDurationFast} ${e.motionEaseInOut} !important`,[`&${r}-appear, &${r}-enter`]:{transform:"translateY(-5px)",opacity:0,"&-active":{transform:"translateY(0)",opacity:1}},[`&${r}-leave-active`]:{transform:"translateY(-5px)"}}}}};const lt=e=>({legend:{display:"block",width:"100%",marginBottom:e.marginLG,padding:0,color:e.colorTextDescription,fontSize:e.fontSizeLG,lineHeight:"inherit",border:0,borderBottom:`${(0,_.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},'input[type="search"]':{boxSizing:"border-box"},'input[type="radio"], input[type="checkbox"]':{lineHeight:"normal"},'input[type="file"]':{display:"block"},'input[type="range"]':{display:"block",width:"100%"},"select[multiple], select[size]":{height:"auto"},"input[type='file']:focus,\n  input[type='radio']:focus,\n  input[type='checkbox']:focus":{outline:0,boxShadow:`0 0 0 ${(0,_.bf)(e.controlOutlineWidth)} ${e.controlOutline}`},output:{display:"block",paddingTop:15,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight}}),we=(e,t)=>{const{formItemCls:n}=e;return{[n]:{[`${n}-label > label`]:{height:t},[`${n}-control-input`]:{minHeight:t}}}},it=e=>{const{componentCls:t}=e;return{[e.componentCls]:Object.assign(Object.assign(Object.assign({},(0,Oe.Wf)(e)),lt(e)),{[`${t}-text`]:{display:"inline-block",paddingInlineEnd:e.paddingSM},"&-small":Object.assign({},we(e,e.controlHeightSM)),"&-large":Object.assign({},we(e,e.controlHeightLG))})}},at=e=>{const{formItemCls:t,iconCls:n,rootPrefixCls:r,antCls:o,labelRequiredMarkColor:i,labelColor:s,labelFontSize:a,labelHeight:f,labelColonMarginInlineStart:g,labelColonMarginInlineEnd:p,itemMarginBottom:N}=e;return{[t]:Object.assign(Object.assign({},(0,Oe.Wf)(e)),{marginBottom:N,verticalAlign:"top","&-with-help":{transition:"none"},[`&-hidden,
        &-hidden${o}-row`]:{display:"none"},"&-has-warning":{[`${t}-split`]:{color:e.colorError}},"&-has-error":{[`${t}-split`]:{color:e.colorWarning}},[`${t}-label`]:{flexGrow:0,overflow:"hidden",whiteSpace:"nowrap",textAlign:"end",verticalAlign:"middle","&-left":{textAlign:"start"},"&-wrap":{overflow:"unset",lineHeight:e.lineHeight,whiteSpace:"unset","> label":{verticalAlign:"middle",textWrap:"balance"}},"> label":{position:"relative",display:"inline-flex",alignItems:"center",maxWidth:"100%",height:f,color:s,fontSize:a,[`> ${n}`]:{fontSize:e.fontSize,verticalAlign:"top"},[`&${t}-required`]:{"&::before":{display:"inline-block",marginInlineEnd:e.marginXXS,color:i,fontSize:e.fontSize,fontFamily:"SimSun, sans-serif",lineHeight:1,content:'"*"'},[`&${t}-required-mark-hidden, &${t}-required-mark-optional`]:{"&::before":{display:"none"}}},[`${t}-optional`]:{display:"inline-block",marginInlineStart:e.marginXXS,color:e.colorTextDescription,[`&${t}-required-mark-hidden`]:{display:"none"}},[`${t}-tooltip`]:{color:e.colorTextDescription,cursor:"help",writingMode:"horizontal-tb",marginInlineStart:e.marginXXS},"&::after":{content:'":"',position:"relative",marginBlock:0,marginInlineStart:g,marginInlineEnd:p},[`&${t}-no-colon::after`]:{content:'"\\a0"'}}},[`${t}-control`]:{"--ant-display":"flex",flexDirection:"column",flexGrow:1,[`&:first-child:not([class^="'${r}-col-'"]):not([class*="' ${r}-col-'"])`]:{width:"100%"},"&-input":{position:"relative",display:"flex",alignItems:"center",minHeight:e.controlHeight,"&-content":{flex:"auto",maxWidth:"100%"}}},[t]:{"&-additional":{display:"flex",flexDirection:"column"},"&-explain, &-extra":{clear:"both",color:e.colorTextDescription,fontSize:e.fontSize,lineHeight:e.lineHeight},"&-explain-connected":{width:"100%"},"&-extra":{minHeight:e.controlHeightSM,transition:`color ${e.motionDurationMid} ${e.motionEaseOut}`},"&-explain":{"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning}}},[`&-with-help ${t}-explain`]:{height:"auto",opacity:1},[`${t}-feedback-icon`]:{fontSize:e.fontSize,textAlign:"center",visibility:"visible",animationName:Ee.kr,animationDuration:e.motionDurationMid,animationTimingFunction:e.motionEaseOutBack,pointerEvents:"none","&-success":{color:e.colorSuccess},"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning},"&-validating":{color:e.colorPrimary}}})}},Me=(e,t)=>{const{formItemCls:n}=e;return{[`${t}-horizontal`]:{[`${n}-label`]:{flexGrow:0},[`${n}-control`]:{flex:"1 1 0",minWidth:0},[`${n}-label[class$='-24'], ${n}-label[class*='-24 ']`]:{[`& + ${n}-control`]:{minWidth:"unset"}}}}},st=e=>{const{componentCls:t,formItemCls:n,inlineItemMarginBottom:r}=e;return{[`${t}-inline`]:{display:"flex",flexWrap:"wrap",[n]:{flex:"none",marginInlineEnd:e.margin,marginBottom:r,"&-row":{flexWrap:"nowrap"},[`> ${n}-label,
        > ${n}-control`]:{display:"inline-block",verticalAlign:"top"},[`> ${n}-label`]:{flex:"none"},[`${t}-text`]:{display:"inline-block"},[`${n}-has-feedback`]:{display:"inline-block"}}}}},q=e=>({padding:e.verticalLabelPadding,margin:e.verticalLabelMargin,whiteSpace:"initial",textAlign:"start","> label":{margin:0,"&::after":{visibility:"hidden"}}}),je=e=>{const{componentCls:t,formItemCls:n,rootPrefixCls:r}=e;return{[`${n} ${n}-label`]:q(e),[`${t}:not(${t}-inline)`]:{[n]:{flexWrap:"wrap",[`${n}-label, ${n}-control`]:{[`&:not([class*=" ${r}-col-xs"])`]:{flex:"0 0 100%",maxWidth:"100%"}}}}}},ct=e=>{const{componentCls:t,formItemCls:n,antCls:r}=e;return{[`${t}-vertical`]:{[`${n}:not(${n}-horizontal)`]:{[`${n}-row`]:{flexDirection:"column"},[`${n}-label > label`]:{height:"auto"},[`${n}-control`]:{width:"100%"},[`${n}-label,
        ${r}-col-24${n}-label,
        ${r}-col-xl-24${n}-label`]:q(e)}},[`@media (max-width: ${(0,_.bf)(e.screenXSMax)})`]:[je(e),{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-xs-24${n}-label`]:q(e)}}}],[`@media (max-width: ${(0,_.bf)(e.screenSMMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-sm-24${n}-label`]:q(e)}}},[`@media (max-width: ${(0,_.bf)(e.screenMDMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-md-24${n}-label`]:q(e)}}},[`@media (max-width: ${(0,_.bf)(e.screenLGMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-lg-24${n}-label`]:q(e)}}}}},dt=e=>{const{formItemCls:t,antCls:n}=e;return{[`${t}-vertical`]:{[`${t}-row`]:{flexDirection:"column"},[`${t}-label > label`]:{height:"auto"},[`${t}-control`]:{width:"100%"}},[`${t}-vertical ${t}-label,
      ${n}-col-24${t}-label,
      ${n}-col-xl-24${t}-label`]:q(e),[`@media (max-width: ${(0,_.bf)(e.screenXSMax)})`]:[je(e),{[t]:{[`${n}-col-xs-24${t}-label`]:q(e)}}],[`@media (max-width: ${(0,_.bf)(e.screenSMMax)})`]:{[t]:{[`${n}-col-sm-24${t}-label`]:q(e)}},[`@media (max-width: ${(0,_.bf)(e.screenMDMax)})`]:{[t]:{[`${n}-col-md-24${t}-label`]:q(e)}},[`@media (max-width: ${(0,_.bf)(e.screenLGMax)})`]:{[t]:{[`${n}-col-lg-24${t}-label`]:q(e)}}}},mt=e=>({labelRequiredMarkColor:e.colorError,labelColor:e.colorTextHeading,labelFontSize:e.fontSize,labelHeight:e.controlHeight,labelColonMarginInlineStart:e.marginXXS/2,labelColonMarginInlineEnd:e.marginXS,itemMarginBottom:e.marginLG,verticalLabelPadding:`0 0 ${e.paddingXS}px`,verticalLabelMargin:0,inlineItemMarginBottom:0}),Ne=(e,t)=>(0,rt.IX)(e,{formItemCls:`${e.componentCls}-item`,rootPrefixCls:t});var he=(0,Fe.I$)("Form",(e,{rootPrefixCls:t})=>{const n=Ne(e,t);return[it(n),at(n),ot(n),Me(n,n.componentCls),Me(n,n.formItemCls),st(n),ct(n),dt(n),(0,nt.Z)(n),Ee.kr]},mt,{order:-1e3});const Pe=[];function be(e,t,n,r=0){return{key:typeof e=="string"?e:`${t}-${r}`,error:e,errorStatus:n}}var Re=({help:e,helpStatus:t,errors:n=Pe,warnings:r=Pe,className:o,fieldId:i,onVisibleChanged:s})=>{const{prefixCls:a}=l.useContext(R.Rk),f=`${a}-item-explain`,g=(0,pe.Z)(a),[p,N,L]=he(a,g),H=l.useMemo(()=>(0,Se.Z)(a),[a]),v=me(n),y=me(r),h=l.useMemo(()=>e!=null?[be(e,"help",t)]:[].concat((0,J.Z)(v.map((c,m)=>be(c,"error","error",m))),(0,J.Z)(y.map((c,m)=>be(c,"warning","warning",m)))),[e,t,v,y]),I=l.useMemo(()=>{const c={};return h.forEach(({key:m})=>{c[m]=(c[m]||0)+1}),h.map((m,M)=>Object.assign(Object.assign({},m),{key:c[m.key]>1?`${m.key}-fallback-${M}`:m.key}))},[h]),C={};return i&&(C.id=`${i}_help`),p(l.createElement(Ie.ZP,{motionDeadline:H.motionDeadline,motionName:`${a}-show-help`,visible:!!I.length,onVisibleChanged:s},c=>{const{className:m,style:M}=c;return l.createElement("div",Object.assign({},C,{className:ne()(f,m,L,g,o,N),style:M}),l.createElement(Ie.V4,Object.assign({keys:I},(0,Se.Z)(a),{motionName:`${a}-show-help-item`,component:!1}),z=>{const{key:j,error:S,errorStatus:O,className:V,style:T}=z;return l.createElement("div",{key:j,className:ne()(V,{[`${f}-${O}`]:O}),style:T},S)}))}))},ie=d(88692),ve=d(53124),Le=d(98866),ut=d(98675),ft=d(97647),gt=d(34203);const Ve=e=>typeof e=="object"&&e!=null&&e.nodeType===1,Te=(e,t)=>(!t||e!=="hidden")&&e!=="visible"&&e!=="clip",ue=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const n=getComputedStyle(e,null);return Te(n.overflowY,t)||Te(n.overflowX,t)||(r=>{const o=(i=>{if(!i.ownerDocument||!i.ownerDocument.defaultView)return null;try{return i.ownerDocument.defaultView.frameElement}catch(s){return null}})(r);return!!o&&(o.clientHeight<r.scrollHeight||o.clientWidth<r.scrollWidth)})(e)}return!1},fe=(e,t,n,r,o,i,s,a)=>i<e&&s>t||i>e&&s<t?0:i<=e&&a<=n||s>=t&&a>=n?i-e-r:s>t&&a<n||i<e&&a>n?s-t+o:0,pt=e=>{const t=e.parentElement;return t==null?e.getRootNode().host||null:t},Ze=(e,t)=>{var n,r,o,i;if(typeof document=="undefined")return[];const{scrollMode:s,block:a,inline:f,boundary:g,skipOverflowHiddenElements:p}=t,N=typeof g=="function"?g:W=>W!==g;if(!Ve(e))throw new TypeError("Invalid target");const L=document.scrollingElement||document.documentElement,H=[];let v=e;for(;Ve(v)&&N(v);){if(v=pt(v),v===L){H.push(v);break}v!=null&&v===document.body&&ue(v)&&!ue(document.documentElement)||v!=null&&ue(v,p)&&H.push(v)}const y=(r=(n=window.visualViewport)==null?void 0:n.width)!=null?r:innerWidth,h=(i=(o=window.visualViewport)==null?void 0:o.height)!=null?i:innerHeight,{scrollX:I,scrollY:C}=window,{height:c,width:m,top:M,right:z,bottom:j,left:S}=e.getBoundingClientRect(),{top:O,right:V,bottom:T,left:G}=(W=>{const u=window.getComputedStyle(W);return{top:parseFloat(u.scrollMarginTop)||0,right:parseFloat(u.scrollMarginRight)||0,bottom:parseFloat(u.scrollMarginBottom)||0,left:parseFloat(u.scrollMarginLeft)||0}})(e);let P=a==="start"||a==="nearest"?M-O:a==="end"?j+T:M+c/2-O+T,E=f==="center"?S+m/2-G+V:f==="end"?z+V:S-G;const D=[];for(let W=0;W<H.length;W++){const u=H[W],{height:B,width:b,top:X,right:k,bottom:ae,left:Q}=u.getBoundingClientRect();if(s==="if-needed"&&M>=0&&S>=0&&j<=h&&z<=y&&(u===L&&!ue(u)||M>=X&&j<=ae&&S>=Q&&z<=k))return D;const ee=getComputedStyle(u),K=parseInt(ee.borderLeftWidth,10),te=parseInt(ee.borderTopWidth,10),U=parseInt(ee.borderRightWidth,10),$=parseInt(ee.borderBottomWidth,10);let F=0,x=0;const w="offsetWidth"in u?u.offsetWidth-u.clientWidth-K-U:0,A="offsetHeight"in u?u.offsetHeight-u.clientHeight-te-$:0,Y="offsetWidth"in u?u.offsetWidth===0?0:b/u.offsetWidth:0,oe="offsetHeight"in u?u.offsetHeight===0?0:B/u.offsetHeight:0;if(L===u)F=a==="start"?P:a==="end"?P-h:a==="nearest"?fe(C,C+h,h,te,$,C+P,C+P+c,c):P-h/2,x=f==="start"?E:f==="center"?E-y/2:f==="end"?E-y:fe(I,I+y,y,K,U,I+E,I+E+m,m),F=Math.max(0,F+C),x=Math.max(0,x+I);else{F=a==="start"?P-X-te:a==="end"?P-ae+$+A:a==="nearest"?fe(X,ae,B,te,$+A,P,P+c,c):P-(X+B/2)+A/2,x=f==="start"?E-Q-K:f==="center"?E-(Q+b/2)+w/2:f==="end"?E-k+U+w:fe(Q,k,b,K,U+w,E,E+m,m);const{scrollLeft:le,scrollTop:Z}=u;F=oe===0?0:Math.max(0,Math.min(Z+F/oe,u.scrollHeight-B/oe+A)),x=Y===0?0:Math.max(0,Math.min(le+x/Y,u.scrollWidth-b/Y+w)),P+=Z-F,E+=le-x}D.push({el:u,top:F,left:x})}return D},ht=e=>e===!1?{block:"end",inline:"nearest"}:(t=>t===Object(t)&&Object.keys(t).length!==0)(e)?e:{block:"start",inline:"nearest"};function bt(e,t){if(!e.isConnected||!(o=>{let i=o;for(;i&&i.parentNode;){if(i.parentNode===document)return!0;i=i.parentNode instanceof ShadowRoot?i.parentNode.host:i.parentNode}return!1})(e))return;const n=(o=>{const i=window.getComputedStyle(o);return{top:parseFloat(i.scrollMarginTop)||0,right:parseFloat(i.scrollMarginRight)||0,bottom:parseFloat(i.scrollMarginBottom)||0,left:parseFloat(i.scrollMarginLeft)||0}})(e);if((o=>typeof o=="object"&&typeof o.behavior=="function")(t))return t.behavior(Ze(e,t));const r=typeof t=="boolean"||t==null?void 0:t.behavior;for(const{el:o,top:i,left:s}of Ze(e,ht(t))){const a=i-n.top+n.bottom,f=s-n.left+n.right;o.scroll({top:a,left:f,behavior:r})}}const vt=["parentNode"],yt="form_item";function de(e){return e===void 0||e===!1?[]:Array.isArray(e)?e:[e]}function He(e,t){if(!e.length)return;const n=e.join("_");return t?`${t}_${n}`:vt.includes(n)?`${yt}_${n}`:n}function We(e,t,n,r,o,i){let s=r;return i!==void 0?s=i:n.validating?s="validating":e.length?s="error":t.length?s="warning":(n.touched||o&&n.validated)&&(s="success"),s}var Ct=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function ze(e){return de(e).join("_")}function De(e,t){const n=t.getFieldInstance(e),r=(0,gt.bn)(n);if(r)return r;const o=He(de(e),t.__INTERNAL__.name);if(o)return document.getElementById(o)}function Ae(e){const[t]=(0,ie.cI)(),n=l.useRef({}),r=l.useMemo(()=>e!=null?e:Object.assign(Object.assign({},t),{__INTERNAL__:{itemRef:o=>i=>{const s=ze(o);i?n.current[s]=i:delete n.current[s]}},scrollToField:(o,i={})=>{const{focus:s}=i,a=Ct(i,["focus"]),f=De(o,r);f&&(bt(f,Object.assign({scrollMode:"if-needed",block:"nearest"},a)),s&&r.focusField(o))},focusField:o=>{var i,s;const a=r.getFieldInstance(o);typeof(a==null?void 0:a.focus)=="function"?a.focus():(s=(i=De(o,r))===null||i===void 0?void 0:i.focus)===null||s===void 0||s.call(i)},getFieldInstance:o=>{const i=ze(o);return n.current[i]}}),[e,t]);return[r]}var $t=d(37920),xt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const It=(e,t)=>{const n=l.useContext(Le.Z),{getPrefixCls:r,direction:o,requiredMark:i,colon:s,scrollToFirstError:a,className:f,style:g}=(0,ve.dj)("form"),{prefixCls:p,className:N,rootClassName:L,size:H,disabled:v=n,form:y,colon:h,labelAlign:I,labelWrap:C,labelCol:c,wrapperCol:m,hideRequiredMark:M,layout:z="horizontal",scrollToFirstError:j,requiredMark:S,onFinishFailed:O,name:V,style:T,feedbackIcons:G,variant:P}=e,E=xt(e,["prefixCls","className","rootClassName","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name","style","feedbackIcons","variant"]),D=(0,ut.Z)(H),W=l.useContext($t.Z),u=l.useMemo(()=>S!==void 0?S:M?!1:i!==void 0?i:!0,[M,S,i]),B=h!=null?h:s,b=r("form",p),X=(0,pe.Z)(b),[k,ae,Q]=he(b,X),ee=ne()(b,`${b}-${z}`,{[`${b}-hide-required-mark`]:u===!1,[`${b}-rtl`]:o==="rtl",[`${b}-${D}`]:D},Q,X,ae,f,N,L),[K]=Ae(y),{__INTERNAL__:te}=K;te.name=V;const U=l.useMemo(()=>({name:V,labelAlign:I,labelCol:c,labelWrap:C,wrapperCol:m,vertical:z==="vertical",colon:B,requiredMark:u,itemRef:te.itemRef,form:K,feedbackIcons:G}),[V,I,c,m,z,B,u,K,G]),$=l.useRef(null);l.useImperativeHandle(t,()=>{var w;return Object.assign(Object.assign({},K),{nativeElement:(w=$.current)===null||w===void 0?void 0:w.nativeElement})});const F=(w,A)=>{if(w){let Y={block:"nearest"};typeof w=="object"&&(Y=Object.assign(Object.assign({},Y),w)),K.scrollToField(A,Y)}},x=w=>{if(O==null||O(w),w.errorFields.length){const A=w.errorFields[0].name;if(j!==void 0){F(j,A);return}a!==void 0&&F(a,A)}};return k(l.createElement(R.pg.Provider,{value:P},l.createElement(Le.n,{disabled:v},l.createElement(ft.Z.Provider,{value:D},l.createElement(R.RV,{validateMessages:W},l.createElement(R.q3.Provider,{value:U},l.createElement(ie.ZP,Object.assign({id:V},E,{name:V,onFinishFailed:x,form:K,ref:$,style:Object.assign(Object.assign({},g),T),className:ee}))))))))};var St=l.forwardRef(It),Ot=d(30470),ye=d(42550),Et=d(96159),Ft=d(27288),wt=d(50344);function Mt(e){if(typeof e=="function")return e;const t=(0,wt.Z)(e);return t.length<=1?t[0]:t}const Be=()=>{const{status:e,errors:t=[],warnings:n=[]}=l.useContext(R.aM);return{status:e,errors:t,warnings:n}};Be.Context=R.aM;var jt=Be,Xe=d(75164);function Nt(e){const[t,n]=l.useState(e),r=l.useRef(null),o=l.useRef([]),i=l.useRef(!1);l.useEffect(()=>(i.current=!1,()=>{i.current=!0,Xe.Z.cancel(r.current),r.current=null}),[]);function s(a){i.current||(r.current===null&&(o.current=[],r.current=(0,Xe.Z)(()=>{r.current=null,n(f=>{let g=f;return o.current.forEach(p=>{g=p(g)}),g})})),o.current.push(a))}return[t,s]}function Pt(){const{itemRef:e}=l.useContext(R.q3),t=l.useRef({});function n(r,o){const i=o&&typeof o=="object"&&(0,ye.C4)(o),s=r.join("_");return(t.current.name!==s||t.current.originRef!==i)&&(t.current.name=s,t.current.originRef=i,t.current.ref=(0,ye.sQ)(e(r),i)),t.current.ref}return n}var Rt=d(5110),Ge=d(8410),Lt=d(98423),Vt=d(17621),Ce=d(56790),Ke=d(21584);const Tt=e=>{const{formItemCls:t}=e;return{"@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)":{[`${t}-control`]:{display:"flex"}}}};var Zt=(0,Fe.bk)(["Form","item-item"],(e,{rootPrefixCls:t})=>{const n=Ne(e,t);return[Tt(n)]}),Ht=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Wt=24;var zt=e=>{const{prefixCls:t,status:n,labelCol:r,wrapperCol:o,children:i,errors:s,warnings:a,_internalItemRender:f,extra:g,help:p,fieldId:N,marginBottom:L,onErrorVisibleChanged:H,label:v}=e,y=`${t}-item`,h=l.useContext(R.q3),I=l.useMemo(()=>{let E=Object.assign({},o||h.wrapperCol||{});return v===null&&!r&&!o&&h.labelCol&&[void 0,"xs","sm","md","lg","xl","xxl"].forEach(W=>{const u=W?[W]:[],B=(0,Ce.U2)(h.labelCol,u),b=typeof B=="object"?B:{},X=(0,Ce.U2)(E,u),k=typeof X=="object"?X:{};"span"in b&&!("offset"in k)&&b.span<Wt&&(E=(0,Ce.t8)(E,[].concat(u,["offset"]),b.span))}),E},[o,h]),C=ne()(`${y}-control`,I.className),c=l.useMemo(()=>{const{labelCol:E,wrapperCol:D}=h;return Ht(h,["labelCol","wrapperCol"])},[h]),m=l.useRef(null),[M,z]=l.useState(0);(0,Ge.Z)(()=>{g&&m.current?z(m.current.clientHeight):z(0)},[g]);const j=l.createElement("div",{className:`${y}-control-input`},l.createElement("div",{className:`${y}-control-input-content`},i)),S=l.useMemo(()=>({prefixCls:t,status:n}),[t,n]),O=L!==null||s.length||a.length?l.createElement(R.Rk.Provider,{value:S},l.createElement(Re,{fieldId:N,errors:s,warnings:a,help:p,helpStatus:n,className:`${y}-explain-connected`,onVisibleChanged:H})):null,V={};N&&(V.id=`${N}_extra`);const T=g?l.createElement("div",Object.assign({},V,{className:`${y}-extra`,ref:m}),g):null,G=O||T?l.createElement("div",{className:`${y}-additional`,style:L?{minHeight:L+M}:{}},O,T):null,P=f&&f.mark==="pro_table_render"&&f.render?f.render(e,{input:j,errorList:O,extra:T}):l.createElement(l.Fragment,null,j,G);return l.createElement(R.q3.Provider,{value:c},l.createElement(Ke.Z,Object.assign({},I,{className:C}),P),l.createElement(Zt,{prefixCls:t}))},Dt=d(87462),At=d(36688),Bt=d(93771),Xt=function(t,n){return l.createElement(Bt.Z,(0,Dt.Z)({},t,{ref:n,icon:At.Z}))},Gt=l.forwardRef(Xt),Kt=Gt;function Yt(e){return e==null?null:typeof e=="object"&&!(0,l.isValidElement)(e)?e:{title:e}}var Qt=Yt,Ut=d(10110),Jt=d(24457),qt=d(83062),kt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},_t=({prefixCls:e,label:t,htmlFor:n,labelCol:r,labelAlign:o,colon:i,required:s,requiredMark:a,tooltip:f,vertical:g})=>{var p;const[N]=(0,Ut.Z)("Form"),{labelAlign:L,labelCol:H,labelWrap:v,colon:y}=l.useContext(R.q3);if(!t)return null;const h=r||H||{},I=o||L,C=`${e}-item-label`,c=ne()(C,I==="left"&&`${C}-left`,h.className,{[`${C}-wrap`]:!!v});let m=t;const M=i===!0||y!==!1&&i!==!1;M&&!g&&typeof t=="string"&&t.trim()&&(m=t.replace(/[:|：]\s*$/,""));const j=Qt(f);if(j){const{icon:P=l.createElement(Kt,null)}=j,E=kt(j,["icon"]),D=l.createElement(qt.Z,Object.assign({},E),l.cloneElement(P,{className:`${e}-item-tooltip`,title:"",onClick:W=>{W.preventDefault()},tabIndex:null}));m=l.createElement(l.Fragment,null,m,D)}const S=a==="optional",O=typeof a=="function",V=a===!1;O?m=a(m,{required:!!s}):S&&!s&&(m=l.createElement(l.Fragment,null,m,l.createElement("span",{className:`${e}-item-optional`,title:""},(N==null?void 0:N.optional)||((p=Jt.Z.Form)===null||p===void 0?void 0:p.optional))));let T;V?T="hidden":(S||O)&&(T="optional");const G=ne()({[`${e}-item-required`]:s,[`${e}-item-required-mark-${T}`]:T,[`${e}-item-no-colon`]:!M});return l.createElement(Ke.Z,Object.assign({},h,{className:c}),l.createElement("label",{htmlFor:n,className:G,title:typeof t=="string"?t:""},m))},en=d(19735),tn=d(17012),nn=d(29950),rn=d(19267);const on={success:en.Z,warning:nn.Z,error:tn.Z,validating:rn.Z};function Ye({children:e,errors:t,warnings:n,hasFeedback:r,validateStatus:o,prefixCls:i,meta:s,noStyle:a}){const f=`${i}-item`,{feedbackIcons:g}=l.useContext(R.q3),p=We(t,n,s,null,!!r,o),{isFormItemInput:N,status:L,hasFeedback:H,feedbackIcon:v}=l.useContext(R.aM),y=l.useMemo(()=>{var h;let I;if(r){const c=r!==!0&&r.icons||g,m=p&&((h=c==null?void 0:c({status:p,errors:t,warnings:n}))===null||h===void 0?void 0:h[p]),M=p&&on[p];I=m!==!1&&M?l.createElement("span",{className:ne()(`${f}-feedback-icon`,`${f}-feedback-icon-${p}`)},m||l.createElement(M,null)):null}const C={status:p||"",errors:t,warnings:n,hasFeedback:!!r,feedbackIcon:I,isFormItemInput:!0};return a&&(C.status=(p!=null?p:L)||"",C.isFormItemInput=N,C.hasFeedback=!!(r!=null?r:H),C.feedbackIcon=r!==void 0?C.feedbackIcon:v),C},[p,r,a,N,L]);return l.createElement(R.aM.Provider,{value:y},e)}var ln=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function an(e){const{prefixCls:t,className:n,rootClassName:r,style:o,help:i,errors:s,warnings:a,validateStatus:f,meta:g,hasFeedback:p,hidden:N,children:L,fieldId:H,required:v,isRequired:y,onSubItemMetaChange:h,layout:I}=e,C=ln(e,["prefixCls","className","rootClassName","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","required","isRequired","onSubItemMetaChange","layout"]),c=`${t}-item`,{requiredMark:m,vertical:M}=l.useContext(R.q3),z=M||I==="vertical",j=l.useRef(null),S=me(s),O=me(a),V=i!=null,T=!!(V||s.length||a.length),G=!!j.current&&(0,Rt.Z)(j.current),[P,E]=l.useState(null);(0,Ge.Z)(()=>{if(T&&j.current){const b=getComputedStyle(j.current);E(parseInt(b.marginBottom,10))}},[T,G]);const D=b=>{b||E(null)},u=((b=!1)=>{const X=b?S:g.errors,k=b?O:g.warnings;return We(X,k,g,"",!!p,f)})(),B=ne()(c,n,r,{[`${c}-with-help`]:V||S.length||O.length,[`${c}-has-feedback`]:u&&p,[`${c}-has-success`]:u==="success",[`${c}-has-warning`]:u==="warning",[`${c}-has-error`]:u==="error",[`${c}-is-validating`]:u==="validating",[`${c}-hidden`]:N,[`${c}-${I}`]:I});return l.createElement("div",{className:B,style:o,ref:j},l.createElement(Vt.Z,Object.assign({className:`${c}-row`},(0,Lt.Z)(C,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol","validateDebounce"])),l.createElement(_t,Object.assign({htmlFor:H},e,{requiredMark:m,required:v!=null?v:y,prefixCls:t,vertical:z})),l.createElement(zt,Object.assign({},e,g,{errors:S,warnings:O,prefixCls:t,status:u,help:i,marginBottom:P,onErrorVisibleChanged:D}),l.createElement(R.qI.Provider,{value:h},l.createElement(Ye,{prefixCls:t,meta:g,errors:g.errors,warnings:g.warnings,hasFeedback:p,validateStatus:u},L)))),!!P&&l.createElement("div",{className:`${c}-margin-offset`,style:{marginBottom:-P}}))}const sn="__SPLIT__",In=null;function cn(e,t){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every(o=>{const i=e[o],s=t[o];return i===s||typeof i=="function"||typeof s=="function"})}const dn=l.memo(({children:e})=>e,(e,t)=>cn(e.control,t.control)&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every((n,r)=>n===t.childProps[r]));function Qe(){return{errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}}function mn(e){const{name:t,noStyle:n,className:r,dependencies:o,prefixCls:i,shouldUpdate:s,rules:a,children:f,required:g,label:p,messageVariables:N,trigger:L="onChange",validateTrigger:H,hidden:v,help:y,layout:h}=e,{getPrefixCls:I}=l.useContext(ve.E_),{name:C}=l.useContext(R.q3),c=Mt(f),m=typeof c=="function",M=l.useContext(R.qI),{validateTrigger:z}=l.useContext(ie.zb),j=H!==void 0?H:z,S=t!=null,O=I("form",i),V=(0,pe.Z)(O),[T,G,P]=he(O,V),E=(0,Ft.ln)("Form.Item"),D=l.useContext(ie.ZM),W=l.useRef(null),[u,B]=Nt({}),[b,X]=(0,Ot.Z)(()=>Qe()),k=$=>{const F=D==null?void 0:D.getKey($.name);if(X($.destroy?Qe():$,!0),n&&y!==!1&&M){let x=$.name;if($.destroy)x=W.current||x;else if(F!==void 0){const[w,A]=F;x=[w].concat((0,J.Z)(A)),W.current=x}M($,x)}},ae=($,F)=>{B(x=>{const w=Object.assign({},x),Y=[].concat((0,J.Z)($.name.slice(0,-1)),(0,J.Z)(F)).join(sn);return $.destroy?delete w[Y]:w[Y]=$,w})},[Q,ee]=l.useMemo(()=>{const $=(0,J.Z)(b.errors),F=(0,J.Z)(b.warnings);return Object.values(u).forEach(x=>{$.push.apply($,(0,J.Z)(x.errors||[])),F.push.apply(F,(0,J.Z)(x.warnings||[]))}),[$,F]},[u,b.errors,b.warnings]),K=Pt();function te($,F,x){return n&&!v?l.createElement(Ye,{prefixCls:O,hasFeedback:e.hasFeedback,validateStatus:e.validateStatus,meta:b,errors:Q,warnings:ee,noStyle:!0},$):l.createElement(an,Object.assign({key:"row"},e,{className:ne()(r,P,V,G),prefixCls:O,fieldId:F,isRequired:x,errors:Q,warnings:ee,meta:b,onSubItemMetaChange:ae,layout:h}),$)}if(!S&&!m&&!o)return T(te(c));let U={};return typeof p=="string"?U.label=p:t&&(U.label=String(t)),N&&(U=Object.assign(Object.assign({},U),N)),T(l.createElement(ie.gN,Object.assign({},e,{messageVariables:U,trigger:L,validateTrigger:j,onMetaChange:k}),($,F,x)=>{const w=de(t).length&&F?F.name:[],A=He(w,C),Y=g!==void 0?g:!!(a!=null&&a.some(Z=>{if(Z&&typeof Z=="object"&&Z.required&&!Z.warningOnly)return!0;if(typeof Z=="function"){const se=Z(x);return(se==null?void 0:se.required)&&!(se!=null&&se.warningOnly)}return!1})),oe=Object.assign({},$);let le=null;if(Array.isArray(c)&&S)le=c;else if(!(m&&(!(s||o)||S))){if(!(o&&!m&&!S))if(l.isValidElement(c)){const Z=Object.assign(Object.assign({},c.props),oe);if(Z.id||(Z.id=A),y||Q.length>0||ee.length>0||e.extra){const ce=[];(y||Q.length>0)&&ce.push(`${A}_help`),e.extra&&ce.push(`${A}_extra`),Z["aria-describedby"]=ce.join(" ")}Q.length>0&&(Z["aria-invalid"]="true"),Y&&(Z["aria-required"]="true"),(0,ye.Yr)(c)&&(Z.ref=K(w,c)),new Set([].concat((0,J.Z)(de(L)),(0,J.Z)(de(j)))).forEach(ce=>{Z[ce]=(...Je)=>{var qe,ke,$e,_e,xe;($e=oe[ce])===null||$e===void 0||(qe=$e).call.apply(qe,[oe].concat(Je)),(xe=(_e=c.props)[ce])===null||xe===void 0||(ke=xe).call.apply(ke,[_e].concat(Je))}});const bn=[Z["aria-required"],Z["aria-invalid"],Z["aria-describedby"]];le=l.createElement(dn,{control:oe,update:c,childProps:bn},(0,Et.Tm)(c,Z))}else m&&(s||o)&&!S?le=c(x):le=c}return te(le,A,Y)}))}const Ue=mn;Ue.useStatus=jt;var un=Ue,fn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},gn=e=>{var{prefixCls:t,children:n}=e,r=fn(e,["prefixCls","children"]);const{getPrefixCls:o}=l.useContext(ve.E_),i=o("form",t),s=l.useMemo(()=>({prefixCls:i,status:"error"}),[i]);return l.createElement(ie.aV,Object.assign({},r),(a,f,g)=>l.createElement(R.Rk.Provider,{value:s},n(a.map(p=>Object.assign(Object.assign({},p),{fieldKey:p.key})),f,{errors:g.errors,warnings:g.warnings})))};function pn(){const{form:e}=l.useContext(R.q3);return e}const re=St;re.Item=un,re.List=gn,re.ErrorList=Re,re.useForm=Ae,re.useFormInstance=pn,re.useWatch=ie.qo,re.Provider=R.RV,re.create=()=>{};var hn=re}}]);
